{"GetBalanceHistory": {"path": "/v1/balance/history", "method": "GET", "operation_id": "GetBalanceHistory", "summary": "List all balance transactions", "description": "<p>Returns a list of transactions that have contributed to the Stripe account balance (e.g., charges, transfers, and so forth). The transactions are returned in sorted order, with the most recent transactions appearing first.</p>\n\n<p>Note that this endpoint was previously called “Balance history” and used the path <code>/v1/balance/history</code>.</p>", "parameters": [{"name": "created", "in": "query", "required": false, "type": "string", "description": "Only return transactions that were created during the given date interval.", "schema": {"anyOf": [{"properties": {"gt": {"type": "integer"}, "gte": {"type": "integer"}, "lt": {"type": "integer"}, "lte": {"type": "integer"}}, "title": "range_query_specs", "type": "object"}, {"type": "integer"}]}}, {"name": "currency", "in": "query", "required": false, "type": "string", "description": "Only return transactions in a certain currency. Three-letter [ISO currency code](https://www.iso.org/iso-4217-currency-codes.html), in lowercase. Must be a [supported currency](https://stripe.com/docs/currencies).", "schema": {"format": "currency", "type": "string"}}, {"name": "ending_before", "in": "query", "required": false, "type": "string", "description": "A cursor for use in pagination. `ending_before` is an object ID that defines your place in the list. For instance, if you make a list request and receive 100 objects, starting with `obj_bar`, your subsequent call can include `ending_before=obj_bar` in order to fetch the previous page of the list.", "schema": {"maxLength": 5000, "type": "string"}}, {"name": "expand", "in": "query", "required": false, "type": "array", "description": "Specifies which fields in the response should be expanded.", "schema": {"items": {"maxLength": 5000, "type": "string"}, "type": "array"}}, {"name": "limit", "in": "query", "required": false, "type": "integer", "description": "A limit on the number of objects to be returned. Limit can range between 1 and 100, and the default is 10.", "schema": {"type": "integer"}}, {"name": "payout", "in": "query", "required": false, "type": "string", "description": "For automatic Stripe payouts only, only returns transactions that were paid out on the specified payout ID.", "schema": {"maxLength": 5000, "type": "string"}}, {"name": "source", "in": "query", "required": false, "type": "string", "description": "Only returns the original transaction.", "schema": {"maxLength": 5000, "type": "string"}}, {"name": "starting_after", "in": "query", "required": false, "type": "string", "description": "A cursor for use in pagination. `starting_after` is an object ID that defines your place in the list. For instance, if you make a list request and receive 100 objects, ending with `obj_foo`, your subsequent call can include `starting_after=obj_foo` in order to fetch the next page of the list.", "schema": {"maxLength": 5000, "type": "string"}}, {"name": "type", "in": "query", "required": false, "type": "string", "description": "Only returns transactions of the given type. One of: `adjustment`, `advance`, `advance_funding`, `anticipation_repayment`, `application_fee`, `application_fee_refund`, `charge`, `climate_order_purchase`, `climate_order_refund`, `connect_collection_transfer`, `contribution`, `issuing_authorization_hold`, `issuing_authorization_release`, `issuing_dispute`, `issuing_transaction`, `obligation_outbound`, `obligation_reversal_inbound`, `payment`, `payment_failure_refund`, `payment_network_reserve_hold`, `payment_network_reserve_release`, `payment_refund`, `payment_reversal`, `payment_unreconciled`, `payout`, `payout_cancel`, `payout_failure`, `payout_minimum_balance_hold`, `payout_minimum_balance_release`, `refund`, `refund_failure`, `reserve_transaction`, `reserved_funds`, `stripe_fee`, `stripe_fx_fee`, `stripe_balance_payment_debit`, `stripe_balance_payment_debit_reversal`, `tax_fee`, `topup`, `topup_reversal`, `transfer`, `transfer_cancel`, `transfer_failure`, or `transfer_refund`.", "schema": {"maxLength": 5000, "type": "string"}}, {"name": "requestBody", "in": "body", "required": false, "type": "object", "description": "Request body", "schema": {"application/x-www-form-urlencoded": {"encoding": {}, "schema": {"additionalProperties": false, "properties": {}, "type": "object"}}}}], "relevance_score": 95, "functionality_type": "utility", "workflow_tags": ["read", "data_retrieval"], "complexity_level": "complex", "recommended_in_templates": ["read_only"], "ai_use_cases": [], "impact_scope": "self-only", "risk_assessment": "medium", "requires_context": [], "workflow_group": null, "priority_rank": 50, "original_tool": {"operationId": "GetBalanceHistory", "path": "/v1/balance/history", "method": "GET", "summary": "List all balance transactions", "description": "<p>Returns a list of transactions that have contributed to the Stripe account balance (e.g., charges, transfers, and so forth). The transactions are returned in sorted order, with the most recent transactions appearing first.</p>\n\n<p>Note that this endpoint was previously called “Balance history” and used the path <code>/v1/balance/history</code>.</p>", "parameters": [{"name": "created", "in": "query", "required": false, "type": "string", "description": "Only return transactions that were created during the given date interval.", "schema": {"anyOf": [{"properties": {"gt": {"type": "integer"}, "gte": {"type": "integer"}, "lt": {"type": "integer"}, "lte": {"type": "integer"}}, "title": "range_query_specs", "type": "object"}, {"type": "integer"}]}}, {"name": "currency", "in": "query", "required": false, "type": "string", "description": "Only return transactions in a certain currency. Three-letter [ISO currency code](https://www.iso.org/iso-4217-currency-codes.html), in lowercase. Must be a [supported currency](https://stripe.com/docs/currencies).", "schema": {"format": "currency", "type": "string"}}, {"name": "ending_before", "in": "query", "required": false, "type": "string", "description": "A cursor for use in pagination. `ending_before` is an object ID that defines your place in the list. For instance, if you make a list request and receive 100 objects, starting with `obj_bar`, your subsequent call can include `ending_before=obj_bar` in order to fetch the previous page of the list.", "schema": {"maxLength": 5000, "type": "string"}}, {"name": "expand", "in": "query", "required": false, "type": "array", "description": "Specifies which fields in the response should be expanded.", "schema": {"items": {"maxLength": 5000, "type": "string"}, "type": "array"}}, {"name": "limit", "in": "query", "required": false, "type": "integer", "description": "A limit on the number of objects to be returned. Limit can range between 1 and 100, and the default is 10.", "schema": {"type": "integer"}}, {"name": "payout", "in": "query", "required": false, "type": "string", "description": "For automatic Stripe payouts only, only returns transactions that were paid out on the specified payout ID.", "schema": {"maxLength": 5000, "type": "string"}}, {"name": "source", "in": "query", "required": false, "type": "string", "description": "Only returns the original transaction.", "schema": {"maxLength": 5000, "type": "string"}}, {"name": "starting_after", "in": "query", "required": false, "type": "string", "description": "A cursor for use in pagination. `starting_after` is an object ID that defines your place in the list. For instance, if you make a list request and receive 100 objects, ending with `obj_foo`, your subsequent call can include `starting_after=obj_foo` in order to fetch the next page of the list.", "schema": {"maxLength": 5000, "type": "string"}}, {"name": "type", "in": "query", "required": false, "type": "string", "description": "Only returns transactions of the given type. One of: `adjustment`, `advance`, `advance_funding`, `anticipation_repayment`, `application_fee`, `application_fee_refund`, `charge`, `climate_order_purchase`, `climate_order_refund`, `connect_collection_transfer`, `contribution`, `issuing_authorization_hold`, `issuing_authorization_release`, `issuing_dispute`, `issuing_transaction`, `obligation_outbound`, `obligation_reversal_inbound`, `payment`, `payment_failure_refund`, `payment_network_reserve_hold`, `payment_network_reserve_release`, `payment_refund`, `payment_reversal`, `payment_unreconciled`, `payout`, `payout_cancel`, `payout_failure`, `payout_minimum_balance_hold`, `payout_minimum_balance_release`, `refund`, `refund_failure`, `reserve_transaction`, `reserved_funds`, `stripe_fee`, `stripe_fx_fee`, `stripe_balance_payment_debit`, `stripe_balance_payment_debit_reversal`, `tax_fee`, `topup`, `topup_reversal`, `transfer`, `transfer_cancel`, `transfer_failure`, or `transfer_refund`.", "schema": {"maxLength": 5000, "type": "string"}}, {"name": "requestBody", "in": "body", "required": false, "type": "object", "description": "Request body", "schema": {"application/x-www-form-urlencoded": {"encoding": {}, "schema": {"additionalProperties": false, "properties": {}, "type": "object"}}}}], "operation": {"description": "<p>Returns a list of transactions that have contributed to the Stripe account balance (e.g., charges, transfers, and so forth). The transactions are returned in sorted order, with the most recent transactions appearing first.</p>\n\n<p>Note that this endpoint was previously called “Balance history” and used the path <code>/v1/balance/history</code>.</p>", "operationId": "GetBalanceHistory", "parameters": [{"description": "Only return transactions that were created during the given date interval.", "explode": true, "in": "query", "name": "created", "required": false, "schema": {"anyOf": [{"properties": {"gt": {"type": "integer"}, "gte": {"type": "integer"}, "lt": {"type": "integer"}, "lte": {"type": "integer"}}, "title": "range_query_specs", "type": "object"}, {"type": "integer"}]}, "style": "deepObject"}, {"description": "Only return transactions in a certain currency. Three-letter [ISO currency code](https://www.iso.org/iso-4217-currency-codes.html), in lowercase. Must be a [supported currency](https://stripe.com/docs/currencies).", "in": "query", "name": "currency", "required": false, "schema": {"format": "currency", "type": "string"}, "style": "form"}, {"description": "A cursor for use in pagination. `ending_before` is an object ID that defines your place in the list. For instance, if you make a list request and receive 100 objects, starting with `obj_bar`, your subsequent call can include `ending_before=obj_bar` in order to fetch the previous page of the list.", "in": "query", "name": "ending_before", "required": false, "schema": {"maxLength": 5000, "type": "string"}, "style": "form"}, {"description": "Specifies which fields in the response should be expanded.", "explode": true, "in": "query", "name": "expand", "required": false, "schema": {"items": {"maxLength": 5000, "type": "string"}, "type": "array"}, "style": "deepObject"}, {"description": "A limit on the number of objects to be returned. Limit can range between 1 and 100, and the default is 10.", "in": "query", "name": "limit", "required": false, "schema": {"type": "integer"}, "style": "form"}, {"description": "For automatic Stripe payouts only, only returns transactions that were paid out on the specified payout ID.", "in": "query", "name": "payout", "required": false, "schema": {"maxLength": 5000, "type": "string"}, "style": "form"}, {"description": "Only returns the original transaction.", "in": "query", "name": "source", "required": false, "schema": {"maxLength": 5000, "type": "string"}, "style": "form"}, {"description": "A cursor for use in pagination. `starting_after` is an object ID that defines your place in the list. For instance, if you make a list request and receive 100 objects, ending with `obj_foo`, your subsequent call can include `starting_after=obj_foo` in order to fetch the next page of the list.", "in": "query", "name": "starting_after", "required": false, "schema": {"maxLength": 5000, "type": "string"}, "style": "form"}, {"description": "Only returns transactions of the given type. One of: `adjustment`, `advance`, `advance_funding`, `anticipation_repayment`, `application_fee`, `application_fee_refund`, `charge`, `climate_order_purchase`, `climate_order_refund`, `connect_collection_transfer`, `contribution`, `issuing_authorization_hold`, `issuing_authorization_release`, `issuing_dispute`, `issuing_transaction`, `obligation_outbound`, `obligation_reversal_inbound`, `payment`, `payment_failure_refund`, `payment_network_reserve_hold`, `payment_network_reserve_release`, `payment_refund`, `payment_reversal`, `payment_unreconciled`, `payout`, `payout_cancel`, `payout_failure`, `payout_minimum_balance_hold`, `payout_minimum_balance_release`, `refund`, `refund_failure`, `reserve_transaction`, `reserved_funds`, `stripe_fee`, `stripe_fx_fee`, `stripe_balance_payment_debit`, `stripe_balance_payment_debit_reversal`, `tax_fee`, `topup`, `topup_reversal`, `transfer`, `transfer_cancel`, `transfer_failure`, or `transfer_refund`.", "in": "query", "name": "type", "required": false, "schema": {"maxLength": 5000, "type": "string"}, "style": "form"}], "requestBody": {"content": {"application/x-www-form-urlencoded": {"encoding": {}, "schema": {"additionalProperties": false, "properties": {}, "type": "object"}}}, "required": false}, "responses": {"200": {"content": {"application/json": {"schema": {"description": "", "properties": {"data": {"items": {"$ref": "#/components/schemas/balance_transaction"}, "type": "array"}, "has_more": {"description": "True if this list has another page of items after this one that can be fetched.", "type": "boolean"}, "object": {"description": "String representing the object's type. Objects of the same type share the same value. Always has the value `list`.", "enum": ["list"], "type": "string"}, "url": {"description": "The URL where this list can be accessed.", "maxLength": 5000, "pattern": "^/v1/balance_transactions", "type": "string"}}, "required": ["data", "has_more", "object", "url"], "title": "BalanceTransactionsList", "type": "object", "x-expandableFields": ["data"]}}}, "description": "Successful response."}, "default": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/error"}}}, "description": "Error response."}}, "summary": "List all balance transactions"}}}}