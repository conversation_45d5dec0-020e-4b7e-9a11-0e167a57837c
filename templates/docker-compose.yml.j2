version: '3.8'

services:
  {{ config.server_name }}:
    build: .
    container_name: {{ config.server_name }}
    restart: unless-stopped
    environment:
      - PYTHONUNBUFFERED=1
      - API_KEY=${API_KEY}
      {% if config.include_auth %}- API_TOKEN=${API_TOKEN}{% endif %}
      {% if config.include_logging %}- LOG_LEVEL=${LOG_LEVEL:-info}{% endif %}
      {% if config.transport == 'streamable-http' %}- HOST=0.0.0.0
      - PORT={{ config.port }}{% endif %}
    {% if config.transport == 'streamable-http' %}ports:
      - "{{ config.port }}:{{ config.port }}"
    {% endif %}volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
    networks:
      - mcp-network
    {% if config.include_logging %}logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    {% endif %}

networks:
  mcp-network:
    driver: bridge
