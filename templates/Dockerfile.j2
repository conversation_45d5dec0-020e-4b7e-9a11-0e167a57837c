FROM python:{{ config.python_version }}-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

{% if config.use_poetry %}
# Install Poetry
RUN pip install poetry

# Configure Poetry
ENV POETRY_NO_INTERACTION=1 \
    POETRY_VENV_IN_PROJECT=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

# Copy Poetry files
COPY pyproject.toml poetry.lock* ./

# Install dependencies
RUN poetry install --only=main && rm -rf $POETRY_CACHE_DIR
{% else %}
# Copy requirements file
COPY requirements.txt .

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt
{% endif %}

# Copy application code
COPY server.py .
COPY .env.example .env

# Create non-root user
RUN adduser --disabled-password --gecos '' --uid 1001 mcp

# Change ownership
RUN chown -R mcp:mcp /app
USER mcp

{% if config.transport == 'streamable-http' %}
# Expose port for HTTP transport
EXPOSE {{ config.port }}
{% endif %}

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)"

# Start the server
{% if config.use_poetry %}
CMD ["poetry", "run", "python", "server.py"]
{% else %}
CMD ["python", "server.py"]
{% endif %}
