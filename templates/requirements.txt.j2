# {{ config.name }} - MCP Server Dependencies
# Generated by Caylex MCP Generator

# Core dependencies
fastmcp>=2.0.0
httpx>=0.25.0

{% if config.include_logging %}
# Logging
structlog>=23.0.0
{% endif %}

{% if config.transport == 'streamable-http' %}
# HTTP server
uvicorn>=0.24.0
{% endif %}

{% if config.include_auth %}
# Authentication
pyjwt>=2.8.0
{% endif %}

# Development dependencies (install with: pip install -r requirements-dev.txt)
# pytest>=7.4.0
# pytest-asyncio>=0.21.0
# black>=23.0.0
# isort>=5.12.0
# mypy>=1.5.0
