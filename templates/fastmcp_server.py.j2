#!/usr/bin/env python3
"""
{{ config.name }} - MCP Server
{{ config.description }}

Generated by Caylex MCP Generator
Generated at: {{ generation_timestamp }}
"""

import asyncio
import os
import logging
from typing import Any, Dict, List, Optional
import httpx
from fastmcp import FastMCP

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize MCP server
mcp = FastMCP("{{ config.name }}")

# Configuration
API_KEY = os.getenv("API_KEY")
if not API_KEY:
    logger.warning("API_KEY environment variable not set")

BASE_URL = "{{ servers[0] }}"
{% if servers|length > 1 %}
# Alternative servers available:
{% for server in servers[1:] %}
# {{ server }}
{% endfor %}
{% endif %}

# HTTP client configuration
client = httpx.AsyncClient(
    timeout=30.0,
    headers={
        "User-Agent": "{{ config.name }}/{{ config.server_version }}",
        {% if config.include_auth %}"Authorization": f"Bearer {API_KEY}" if API_KEY else None,{% endif %}
    }
)

{% for endpoint in endpoints %}
@mcp.tool()
async def {{ endpoint.operation_id }}(
    {% for param in endpoint.parameters %}
    {% if param.in == 'path' %}{{ param.name }}: {{ 'str' if param.schema.type == 'string' else param.schema.type }}{% if not param.required %} = None{% endif %},
    {% elif param.in == 'query' %}{{ param.name }}: {{ 'str' if param.schema.type == 'string' else param.schema.type }}{% if not param.required %} = None{% endif %},
    {% endif %}
    {% endfor %}
    {% if endpoint.method in ['POST', 'PUT', 'PATCH'] %}request_body: Optional[Dict[str, Any]] = None,{% endif %}
) -> Dict[str, Any]:
    """
    {{ endpoint.summary or endpoint.description or 'API endpoint' }}
    
    {% if endpoint.description %}{{ endpoint.description }}{% endif %}
    
    {% if endpoint.parameters %}Parameters:
    {% for param in endpoint.parameters %}
    - {{ param.name }} ({{ param.schema.type }}{% if param.required %}, required{% endif %}): {{ param.description or 'No description' }}
    {% endfor %}
    {% endif %}
    
    Returns:
        API response data
    """
    try:
        # Build URL
        url = f"{BASE_URL}{{ endpoint.path }}"
        {% for param in endpoint.parameters %}
        {% if param.in == 'path' %}
        url = url.replace("{{ '{' + param.name + '}' }}", str({{ param.name }}))
        {% endif %}
        {% endfor %}
        
        # Build query parameters
        params = {}
        {% for param in endpoint.parameters %}
        {% if param.in == 'query' %}
        if {{ param.name }} is not None:
            params["{{ param.name }}"] = {{ param.name }}
        {% endif %}
        {% endfor %}
        
        # Build headers
        headers = {}
        {% for param in endpoint.parameters %}
        {% if param.in == 'header' %}
        if {{ param.name }} is not None:
            headers["{{ param.name }}"] = {{ param.name }}
        {% endif %}
        {% endfor %}
        
        # Make request
        {% if endpoint.method == 'GET' %}
        response = await client.get(url, params=params, headers=headers)
        {% elif endpoint.method == 'POST' %}
        response = await client.post(url, params=params, headers=headers, json=request_body)
        {% elif endpoint.method == 'PUT' %}
        response = await client.put(url, params=params, headers=headers, json=request_body)
        {% elif endpoint.method == 'PATCH' %}
        response = await client.patch(url, params=params, headers=headers, json=request_body)
        {% elif endpoint.method == 'DELETE' %}
        response = await client.delete(url, params=params, headers=headers)
        {% endif %}
        
        response.raise_for_status()
        
        # Return response data
        try:
            return response.json()
        except Exception:
            return {"data": response.text, "status_code": response.status_code}
            
    except httpx.HTTPStatusError as e:
        logger.error(f"HTTP error in {{ endpoint.operation_id }}: {e}")
        return {
            "error": f"HTTP {e.response.status_code}: {e.response.text}",
            "status_code": e.response.status_code
        }
    except Exception as e:
        logger.error(f"Error in {{ endpoint.operation_id }}: {e}")
        return {"error": str(e)}

{% endfor %}

async def main():
    """Main entry point for the MCP server."""
    {% if config.transport == 'stdio' %}
    # Run with stdio transport
    await mcp.run()
    {% elif config.transport == 'streamable-http' %}
    # Run with HTTP transport
    await mcp.run_server(host="0.0.0.0", port={{ config.port }})
    {% else %}
    # Default to stdio
    await mcp.run()
    {% endif %}

if __name__ == "__main__":
    asyncio.run(main())
