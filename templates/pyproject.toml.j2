[tool.poetry]
name = "{{ config.server_name }}"
version = "{{ config.server_version }}"
description = "{{ config.description }}"
authors = ["Generated by Caylex MCP Generator"]
readme = "README.md"
packages = [{include = "{{ config.server_name.replace('-', '_') }}"}]

[tool.poetry.dependencies]
python = "^{{ config.python_version }}"
fastmcp = "^2.0.0"
httpx = "^0.25.0"
{% if config.include_logging %}
structlog = "^23.0.0"
{% endif %}
{% if config.transport == 'streamable-http' %}
uvicorn = "^0.24.0"
{% endif %}

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
black = "^23.0.0"
isort = "^5.12.0"
mypy = "^1.5.0"

[tool.poetry.scripts]
{{ config.server_name }} = "{{ config.server_name.replace('-', '_') }}.server:main"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py{{ config.python_version.replace('.', '') }}']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "{{ config.python_version }}"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
