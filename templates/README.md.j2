# {{ config.name }}

{{ config.description }}

## Overview

This MCP server provides access to {{ endpoints|length }} API endpoints through the Model Context Protocol (MCP) using FastMCP 2.0.

**Generated by:** Caylex MCP Generator
**Generated at:** {{ generation_timestamp }}
**Transport:** {{ config.transport }}
**Tools:** {{ endpoints|length }}

## Features

- ✅ FastMCP 2.0 compatible
- ✅ {{ config.transport|title }} transport
- ✅ {{ endpoints|length }} curated API tools
- ✅ Python {{ config.python_version }}+ support
{% if config.include_auth %}- ✅ Authentication support{% endif %}
{% if config.include_logging %}- ✅ Structured logging{% endif %}
{% if config.include_cors %}- ✅ CORS support{% endif %}
{% if config.use_poetry %}- ✅ Poetry dependency management{% endif %}

## Quick Start

### Option 1: Using Poetry (Recommended)

1. **Install Poetry** (if not already installed):
   ```bash
   curl -sSL https://install.python-poetry.org | python3 -
   ```

2. **Install dependencies:**
   ```bash
   poetry install
   ```

3. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your API credentials
   ```

4. **Start the server:**
   ```bash
   poetry run python server.py
   ```

### Option 2: Using pip

1. **Create virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your API credentials
   ```

4. **Start the server:**
   ```bash
   python server.py
   ```

## Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
{% for key, desc in metadata.environment_variables.items() %}| `{{ key }}` | {{ desc }} | {% if key == 'API_KEY' %}Yes{% else %}No{% endif %} |
{% endfor %}

### Transport Configuration

**Transport Type:** {{ config.transport }}
{% if config.transport == 'streamable-http' %}
**Port:** {{ config.port }}
**Host:** 0.0.0.0 (configurable via HOST env var)
{% endif %}

## Available Tools

This server provides {{ endpoints|length }} tools:

{% for endpoint in endpoints %}
### `{{ endpoint.operation_id }}`
- **Method:** {{ endpoint.method }}
- **Path:** `{{ endpoint.path }}`
- **Description:** {{ endpoint.summary or endpoint.description or 'No description available' }}
{% if endpoint.parameters %}
- **Parameters:**
{% for param in endpoint.parameters %}
  - `{{ param.name }}` ({{ param.schema.type }}{% if param.required %}, required{% endif %}): {{ param.description or 'No description' }}
{% endfor %}
{% endif %}

{% endfor %}

## Development

### Project Structure

```
{{ config.server_name }}/
├── server.py             # Main server file
{% if config.use_poetry %}├── pyproject.toml        # Poetry configuration
├── poetry.lock           # Poetry lock file
{% endif %}├── requirements.txt      # Pip requirements
├── .env.example          # Environment template
├── README.md             # This file
{% if config.include_docker %}├── Dockerfile            # Docker configuration
├── docker-compose.yml    # Docker Compose setup
{% endif %}└── logs/                 # Application logs (if logging enabled)
```

### Development Commands

{% if config.use_poetry %}
**Using Poetry:**
- `poetry run python server.py` - Start the MCP server
- `poetry run pytest` - Run tests (when implemented)
- `poetry run black .` - Format code
- `poetry run isort .` - Sort imports
- `poetry run mypy .` - Type checking

**Using pip:**
{% endif %}
- `python server.py` - Start the MCP server
- `pytest` - Run tests (when implemented)
- `black .` - Format code
- `isort .` - Sort imports
- `mypy .` - Type checking

### Dependencies

{% for dep in metadata.dependencies %}
- {{ dep }}
{% endfor %}

## Deployment

{% if config.include_docker %}
### Docker (Recommended)

1. **Build the image:**
   ```bash
   docker build -t {{ config.server_name }} .
   ```

2. **Run with Docker Compose:**
   ```bash
   docker-compose up -d
   ```

### Manual Deployment
{% endif %}

1. **Install Python {{ config.python_version }}+**

2. **Install dependencies:**
   ```bash
   {% if config.use_poetry %}poetry install --only=main{% else %}pip install -r requirements.txt{% endif %}
   ```

3. **Set environment variables and start:**
   ```bash
   export API_KEY="your-api-key"
   {% if config.use_poetry %}poetry run python server.py{% else %}python server.py{% endif %}
   ```

## Usage with MCP Clients

### Claude Desktop

Add to your Claude Desktop configuration:

```json
{
  "mcpServers": {
    "{{ config.server_name }}": {
      {% if config.transport == 'streamable-http' %}"command": "python",
      "args": ["{{ config.server_name }}/server.py"],
      "env": {
        "API_KEY": "your-api-key"
      }{% else %}"command": "python",
      "args": ["{{ config.server_name }}/server.py"],
      "env": {
        "API_KEY": "your-api-key"
      }{% endif %}
    }
  }
}
```

### Other MCP Clients

{% if config.transport == 'streamable-http' %}For HTTP transport, connect to: `http://localhost:{{ config.port }}`
{% else %}Use stdio transport with the Python server.
{% endif %}

## Troubleshooting

### Common Issues

1. **"API_KEY not set" warning**
   - Ensure your `.env` file contains a valid `API_KEY`
   - Check that environment variables are loaded correctly

2. **Connection errors**
   - Verify the API base URL is correct
   - Check your network connection and firewall settings
   - Ensure API credentials are valid

3. **Python version issues**
   - Ensure you're using Python {{ config.python_version }} or higher
   - Check your virtual environment is activated

4. **Dependency issues**
   - {% if config.use_poetry %}Run `poetry install` to ensure all dependencies are installed{% else %}Run `pip install -r requirements.txt` to install dependencies{% endif %}
   - Try creating a fresh virtual environment

5. **Port already in use (HTTP transport)**
   - Change the `PORT` environment variable
   - Kill any processes using port {{ config.port }}

### Logs

{% if config.include_logging %}Application logs are written to the console and optionally to the `logs/` directory.
{% else %}Check console output for error messages and debugging information.
{% endif %}

## Support

This server was generated by the Caylex MCP Generator. For issues:

1. Check the troubleshooting section above
2. Review the server logs for error details
3. Verify your API credentials and network connectivity
4. Consult the FastMCP documentation for transport-specific issues

## License

Generated code is provided as-is. Please ensure compliance with the underlying API's terms of service.

---

*Generated on {{ generation_timestamp }} by Caylex MCP Generator*
