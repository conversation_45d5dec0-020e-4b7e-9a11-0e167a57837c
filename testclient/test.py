import requests

url = "http://localhost:8002/session"

headers = {
    "x-api-key": "1",
    "x-username": "<EMAIL>"
}

response = requests.post(url, headers=headers)

session_id = response.json()["session_id"]

#print("Session ID: ", session_id)

url = "http://localhost:8002/upload"

files = { "file": open('testclient/stripe.json', 'rb') }
payload = { "session_id": session_id }
headers = {
    "x-api-key": "1",
    "x-username": "<EMAIL>"
}

response = requests.post(url, data=payload, files=files, headers=headers)

#print("Upload: ", response.json())
url = "http://localhost:8002/curate"

payload = {
    "session_id": session_id,
    "selected_tools": ["GetBalanceHistory"],
    "template_type": "full_api",
    "customizations": {
        "server_name": "stripe-mcp-server",
        "include_auth": True,
        "risk_level": "medium",
        "max_tools": 10,
        "exclude_patterns": [],
        "include_patterns": [],
        "custom_descriptions": {}
    }
}
headers = {"content-type": "application/json"}

response = requests.post(url, json=payload, headers=headers)

url = "http://localhost:8002/session/" + session_id

response = requests.get(url)

print("Session: ", response.json())

url = "http://localhost:8002/generate"

payload = {
    "session_id": session_id,
    "config": {
        "server_name": "strike-mcp-server",
        "server_version": "1.0.0",
        "transport": "streamable-http",
        "port": 3000,
        "name": "Strike MCP Server",
        "description": "MCP server for Strike API generated from OpenAPI specification",
        "include_auth": True,
        "include_logging": True,
        "include_cors": True
    }
}
headers = {"content-type": "application/json"}

response = requests.post(url, json=payload, headers=headers)

print("Generate: ", response.json())
