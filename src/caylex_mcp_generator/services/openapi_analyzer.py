"""
OpenAPI specification analysis service.
"""

import json
import re
from typing import Any, Dict, List, Optional, Tuple

import structlog
import yaml
from jsonschema import ValidationError
from openapi_spec_validator import validate_spec
from openapi_spec_validator.exceptions import OpenAPISpecValidatorError

from ..config import settings
from ..models.analysis import (
    AIRecommendation,
    AnalysisResult,
    EnhancedAnalysis,
    FilteredTool,
    OpenAPIEndpoint,
    RelevanceDistribution,
    SelectionTemplate,
    WorkflowGroup,
)
from .enhanced_analyzer import EnhancedAnalyzer

logger = structlog.get_logger(__name__)


class OpenAPIAnalyzer:
    """Analyzes OpenAPI specifications and extracts tool information."""

    def __init__(self):
        self.complexity_weights = {
            "parameters": 1,
            "request_body": 2,
            "responses": 1,
            "security": 3,
            "nested_objects": 2,
        }
        # Initialize the enhanced analyzer
        self.enhanced_analyzer = EnhancedAnalyzer()
    
    async def analyze_openapi_spec(self, content: bytes, filename: str) -> AnalysisResult:
        """Analyze an OpenAPI specification file."""
        logger.info("Starting OpenAPI analysis", filename=filename)
        
        try:
            # Parse the content
            spec = await self._parse_spec_content(content, filename)
            
            # Validate the specification
            await self._validate_spec(spec)
            
            # Extract basic information
            servers = self._extract_servers(spec)
            
            # Analyze endpoints
            endpoints, filtered_tools = await self._analyze_endpoints(spec)
            
            # Perform enhanced analysis
            enhanced_analysis = await self._perform_enhanced_analysis(endpoints, spec)
            
            # Determine overall complexity
            complexity = self._determine_overall_complexity(endpoints)
            
            result = AnalysisResult(
                openapi=spec,
                endpoints=endpoints,
                servers=servers,
                tool_count=len(endpoints),
                complexity=complexity,
                filtered_tools=filtered_tools,
                enhanced_analysis=enhanced_analysis
            )
            
            logger.info(
                "Completed OpenAPI analysis",
                filename=filename,
                tool_count=len(endpoints),
                filtered_count=len(filtered_tools),
                complexity=complexity,
                server_count=len(servers),
                high_relevance_count=len(enhanced_analysis.high_relevance_tools)
            )
            
            return result
            
        except Exception as e:
            logger.error("Failed to analyze OpenAPI spec", filename=filename, error=str(e))
            raise
    
    async def _parse_spec_content(self, content: bytes, filename: str) -> Dict[str, Any]:
        """Parse OpenAPI specification content."""
        try:
            content_str = content.decode('utf-8')
            
            # Try JSON first
            if filename.lower().endswith('.json'):
                return json.loads(content_str)
            
            # Try YAML
            elif filename.lower().endswith(('.yaml', '.yml')):
                return yaml.safe_load(content_str)
            
            # Auto-detect format
            else:
                try:
                    return json.loads(content_str)
                except json.JSONDecodeError:
                    return yaml.safe_load(content_str)
                    
        except Exception as e:
            raise ValueError(f"Failed to parse OpenAPI specification: {str(e)}")
    
    async def _validate_spec(self, spec: Dict[str, Any]) -> None:
        """Validate OpenAPI specification."""
        try:
            validate_spec(spec)
        except OpenAPISpecValidatorError as e:
            logger.warning("OpenAPI validation warning", error=str(e))
            # Continue with analysis despite validation warnings
        except Exception as e:
            raise ValueError(f"Invalid OpenAPI specification: {str(e)}")
    
    def _extract_servers(self, spec: Dict[str, Any]) -> List[str]:
        """Extract server URLs from the specification."""
        servers = []
        
        # OpenAPI 3.x servers
        if 'servers' in spec:
            for server in spec['servers']:
                if 'url' in server:
                    servers.append(server['url'])
        
        # OpenAPI 2.x host/basePath
        elif 'host' in spec:
            scheme = spec.get('schemes', ['https'])[0]
            base_path = spec.get('basePath', '')
            servers.append(f"{scheme}://{spec['host']}{base_path}")
        
        return servers or ["https://api.example.com"]
    
    async def _analyze_endpoints(self, spec: Dict[str, Any]) -> Tuple[List[OpenAPIEndpoint], List[FilteredTool]]:
        """Analyze endpoints and extract tool information."""
        endpoints = []
        filtered_tools = []
        
        paths = spec.get('paths', {})
        
        for path, path_item in paths.items():
            if not isinstance(path_item, dict):
                continue
                
            for method, operation in path_item.items():
                if method.startswith('x-') or not isinstance(operation, dict):
                    continue
                
                try:
                    endpoint = await self._analyze_single_endpoint(path, method, operation, spec)
                    if endpoint:
                        endpoints.append(endpoint)
                except Exception as e:
                    # Log filtered tool
                    operation_id = operation.get('operationId', f"{method}_{path}")
                    filtered_tool = FilteredTool(
                        operation_id=operation_id,
                        method=method.upper(),
                        path=path,
                        reason=f"Analysis failed: {str(e)}",
                        unresolved_refs=[]
                    )
                    filtered_tools.append(filtered_tool)
                    logger.warning(
                        "Filtered endpoint due to analysis error",
                        path=path,
                        method=method,
                        error=str(e)
                    )
        
        # Limit endpoints if configured
        if len(endpoints) > settings.max_endpoints:
            # Sort by relevance score and take top endpoints
            endpoints.sort(key=lambda x: x.relevance_score, reverse=True)
            filtered_count = len(endpoints) - settings.max_endpoints
            endpoints = endpoints[:settings.max_endpoints]
            logger.info(f"Limited endpoints to {settings.max_endpoints}, filtered {filtered_count} additional endpoints")
        
        return endpoints, filtered_tools
    
    async def _analyze_single_endpoint(self, path: str, method: str, operation: Dict[str, Any], spec: Dict[str, Any]) -> Optional[OpenAPIEndpoint]:
        """Analyze a single endpoint."""
        operation_id = operation.get('operationId', f"{method}_{path.replace('/', '_').replace('{', '').replace('}', '')}")
        summary = operation.get('summary', f"{method.upper()} {path}")
        description = operation.get('description', '')
        
        # Extract parameters
        parameters = self._extract_parameters(operation, spec)
        
        # Calculate relevance score
        relevance_score = self._calculate_relevance_score(path, method, operation, parameters)
        
        # Skip if below threshold
        if relevance_score < settings.relevance_threshold:
            return None
        
        # Determine functionality type
        functionality_type = self._determine_functionality_type(path, method, operation)
        
        # Generate workflow tags
        workflow_tags = self._generate_workflow_tags(path, method, operation)
        
        # Determine complexity
        complexity_level = self._determine_complexity_level(operation, parameters)
        
        # Generate template recommendations
        recommended_templates = self._recommend_templates(functionality_type, complexity_level, workflow_tags)
        
        # Create original tool definition
        original_tool = {
            "operationId": operation_id,
            "path": path,
            "method": method.upper(),
            "summary": summary,
            "description": description,
            "parameters": parameters,
            "operation": operation
        }
        
        return OpenAPIEndpoint(
            path=path,
            method=method.upper(),
            operation_id=operation_id,
            summary=summary,
            description=description,
            parameters=parameters,
            relevance_score=relevance_score,
            functionality_type=functionality_type,
            workflow_tags=workflow_tags,
            complexity_level=complexity_level,
            recommended_in_templates=recommended_templates,
            original_tool=original_tool
        )

    def _extract_parameters(self, operation: Dict[str, Any], spec: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract and normalize parameters from operation."""
        parameters = []

        # Get parameters from operation
        op_params = operation.get('parameters', [])

        for param in op_params:
            # Resolve $ref if present
            if '$ref' in param:
                # Simple ref resolution (would need more robust implementation)
                ref_path = param['$ref'].split('/')[-1]
                if 'components' in spec and 'parameters' in spec['components']:
                    param = spec['components']['parameters'].get(ref_path, param)

            parameters.append({
                'name': param.get('name', ''),
                'in': param.get('in', 'query'),
                'required': param.get('required', False),
                'type': self._get_parameter_type(param),
                'description': param.get('description', ''),
                'schema': param.get('schema', {})
            })

        # Add request body as parameter if present
        if 'requestBody' in operation:
            request_body = operation['requestBody']
            parameters.append({
                'name': 'requestBody',
                'in': 'body',
                'required': request_body.get('required', False),
                'type': 'object',
                'description': request_body.get('description', 'Request body'),
                'schema': request_body.get('content', {})
            })

        return parameters

    def _get_parameter_type(self, param: Dict[str, Any]) -> str:
        """Get parameter type from parameter definition."""
        if 'schema' in param:
            return param['schema'].get('type', 'string')
        return param.get('type', 'string')

    def _calculate_relevance_score(self, path: str, method: str, operation: Dict[str, Any], parameters: List[Dict[str, Any]]) -> int:
        """Calculate relevance score for an endpoint."""
        score = 50  # Base score

        # Method scoring
        method_scores = {
            'GET': 10,
            'POST': 15,
            'PUT': 12,
            'PATCH': 12,
            'DELETE': 8,
        }
        score += method_scores.get(method.upper(), 5)

        # Path scoring
        if '/health' in path.lower() or '/status' in path.lower():
            score -= 20  # Health checks are less relevant
        elif '/admin' in path.lower():
            score -= 10  # Admin endpoints are less relevant
        elif '/api' in path.lower():
            score += 5   # API endpoints are more relevant

        # Parameter complexity
        score += min(len(parameters) * 2, 15)

        # Documentation quality
        if operation.get('summary'):
            score += 5
        if operation.get('description'):
            score += 5
        if operation.get('operationId'):
            score += 5

        # Response definitions
        responses = operation.get('responses', {})
        if '200' in responses or '201' in responses:
            score += 5

        # Security requirements
        if operation.get('security'):
            score += 3

        return min(max(score, 0), 100)

    def _determine_functionality_type(self, path: str, method: str, operation: Dict[str, Any]) -> str:
        """Determine the functionality type of an endpoint."""
        path_lower = path.lower()
        method_upper = method.upper()

        # Admin functionality
        if '/admin' in path_lower or '/management' in path_lower:
            return 'admin'

        # Authentication/authorization
        if '/auth' in path_lower or '/login' in path_lower or '/token' in path_lower:
            return 'auth'

        # Health/monitoring
        if '/health' in path_lower or '/status' in path_lower or '/metrics' in path_lower:
            return 'monitoring'

        # CRUD operations
        if method_upper in ['POST', 'PUT', 'PATCH', 'DELETE']:
            return 'core'
        elif method_upper == 'GET':
            if '{' in path:  # Path parameters suggest specific resource access
                return 'core'
            else:
                return 'utility'

        return 'utility'

    def _generate_workflow_tags(self, path: str, method: str, operation: Dict[str, Any]) -> List[str]:
        """Generate workflow tags for an endpoint."""
        tags = []

        # Method-based tags
        method_tags = {
            'GET': ['data_retrieval', 'read'],
            'POST': ['data_creation', 'write'],
            'PUT': ['data_update', 'write'],
            'PATCH': ['data_update', 'write'],
            'DELETE': ['data_deletion', 'write']
        }
        tags.extend(method_tags.get(method.upper(), []))

        # Path-based tags
        path_lower = path.lower()
        if '/user' in path_lower:
            tags.append('user_management')
        if '/search' in path_lower:
            tags.append('search')
        if '/report' in path_lower:
            tags.append('reporting')
        if '/export' in path_lower:
            tags.append('export')
        if '/import' in path_lower:
            tags.append('import')

        # Operation tags
        if 'tags' in operation:
            tags.extend([tag.lower().replace(' ', '_') for tag in operation['tags']])

        return list(set(tags))  # Remove duplicates

    def _determine_complexity_level(self, operation: Dict[str, Any], parameters: List[Dict[str, Any]]) -> str:
        """Determine complexity level of an endpoint."""
        complexity_score = 0

        # Parameter complexity
        complexity_score += len(parameters)

        # Request body complexity
        if 'requestBody' in operation:
            complexity_score += 2

        # Response complexity
        responses = operation.get('responses', {})
        complexity_score += len(responses)

        # Security complexity
        if operation.get('security'):
            complexity_score += 1

        # Determine level
        if complexity_score <= 3:
            return 'simple'
        elif complexity_score <= 7:
            return 'moderate'
        else:
            return 'complex'

    def _recommend_templates(self, functionality_type: str, complexity_level: str, workflow_tags: List[str]) -> List[str]:
        """Recommend templates based on endpoint characteristics."""
        templates = []

        # Base recommendations
        if functionality_type == 'core':
            templates.append('basic_crud')
            if complexity_level != 'simple':
                templates.append('full_api')

        if functionality_type in ['utility', 'monitoring']:
            templates.append('read_only')

        if functionality_type == 'admin':
            templates.append('admin_tools')

        # Workflow-based recommendations
        if 'data_retrieval' in workflow_tags and 'data_creation' not in workflow_tags:
            templates.append('read_only')

        if len(set(workflow_tags) & {'data_creation', 'data_update', 'data_deletion'}) >= 2:
            templates.append('basic_crud')

        return list(set(templates)) or ['basic_crud']

    async def _perform_enhanced_analysis(self, endpoints: List[OpenAPIEndpoint], spec: Dict[str, Any]) -> EnhancedAnalysis:
        """Perform AI-enhanced analysis of the endpoints using the new enhanced analyzer."""
        logger.info("🚀 Starting enhanced analysis with pattern and LLM analyzers")

        try:
            # Use the new enhanced analyzer which integrates both pattern and LLM analysis
            enhanced_analysis = await self.enhanced_analyzer.perform_enhanced_analysis(endpoints, spec)

            logger.info("✅ Enhanced analysis completed successfully")
            return enhanced_analysis

        except Exception as e:
            logger.error(f"🚨 Enhanced analysis failed, falling back to basic analysis: {str(e)}")

            # Fallback to basic analysis if enhanced analysis fails
            return self._create_fallback_enhanced_analysis(endpoints, spec)

    def _create_fallback_enhanced_analysis(self, endpoints: List[OpenAPIEndpoint], spec: Dict[str, Any]) -> EnhancedAnalysis:
        """Create fallback enhanced analysis using original methods."""
        logger.info("🔄 Creating fallback enhanced analysis")

        # Generate workflow groups
        workflow_groups = self._generate_workflow_groups(endpoints)

        # Generate selection templates
        selection_templates = self._generate_selection_templates(endpoints)

        # Generate AI recommendations
        recommendations = self._generate_ai_recommendations(endpoints, spec)

        # Get high relevance tools
        high_relevance_tools = [ep.operation_id for ep in endpoints if ep.relevance_score >= 80]

        # Calculate relevance distribution
        relevance_distribution = self._calculate_relevance_distribution(endpoints)

        return EnhancedAnalysis(
            workflow_groups=workflow_groups,
            selection_templates=selection_templates,
            recommendations=recommendations,
            high_relevance_tools=high_relevance_tools,
            relevance_distribution=relevance_distribution
        )

    def _generate_workflow_groups(self, endpoints: List[OpenAPIEndpoint]) -> Dict[str, WorkflowGroup]:
        """Generate workflow-based groupings of endpoints."""
        groups = {}

        # Group by functionality type
        func_groups = {}
        for endpoint in endpoints:
            func_type = endpoint.functionality_type
            if func_type not in func_groups:
                func_groups[func_type] = []
            func_groups[func_type].append(endpoint)

        # Create workflow groups
        priority_map = {'core': 1, 'utility': 2, 'auth': 3, 'admin': 4, 'monitoring': 5}

        for func_type, group_endpoints in func_groups.items():
            if len(group_endpoints) > 0:
                groups[func_type] = WorkflowGroup(
                    name=func_type.replace('_', ' ').title(),
                    description=f"{func_type.replace('_', ' ').title()} functionality endpoints",
                    tools=[ep.operation_id for ep in group_endpoints],
                    priority=priority_map.get(func_type, 5)
                )

        return groups

    def _generate_selection_templates(self, endpoints: List[OpenAPIEndpoint]) -> Dict[str, SelectionTemplate]:
        """Generate selection templates based on endpoint analysis."""
        templates = {}

        # Basic CRUD template
        crud_tools = [ep.operation_id for ep in endpoints if ep.functionality_type == 'core']
        if crud_tools:
            templates['basic_crud'] = SelectionTemplate(
                name='Basic CRUD',
                description='Essential create, read, update, delete operations',
                recommended_tools=crud_tools[:10],  # Limit to top 10
                use_case='Basic data management operations'
            )

        # Read-only template
        read_tools = [ep.operation_id for ep in endpoints if 'GET' in ep.method and ep.functionality_type in ['core', 'utility']]
        if read_tools:
            templates['read_only'] = SelectionTemplate(
                name='Read Only',
                description='Read-only operations for data retrieval',
                recommended_tools=read_tools[:15],
                use_case='Data retrieval and reporting'
            )

        # High relevance template
        high_rel_tools = [ep.operation_id for ep in endpoints if ep.relevance_score >= 80]
        if high_rel_tools:
            templates['high_relevance'] = SelectionTemplate(
                name='High Relevance',
                description='Most relevant and useful endpoints',
                recommended_tools=high_rel_tools,
                use_case='Most important API functionality'
            )

        return templates

    def _generate_ai_recommendations(self, endpoints: List[OpenAPIEndpoint], spec: Dict[str, Any]) -> AIRecommendation:
        """Generate AI-powered recommendations."""
        # Analyze API characteristics
        total_endpoints = len(endpoints)
        avg_relevance = sum(ep.relevance_score for ep in endpoints) / total_endpoints if total_endpoints > 0 else 0
        complexity_counts = {}
        for ep in endpoints:
            complexity_counts[ep.complexity_level] = complexity_counts.get(ep.complexity_level, 0) + 1

        # Generate summary
        summary = f"API with {total_endpoints} endpoints, average relevance score {avg_relevance:.1f}"

        # Identify strengths
        strengths = []
        if avg_relevance >= 70:
            strengths.append("High overall relevance score indicates well-designed API")
        if complexity_counts.get('simple', 0) > total_endpoints * 0.6:
            strengths.append("Majority of endpoints have simple complexity")
        if len([ep for ep in endpoints if ep.functionality_type == 'core']) > 0:
            strengths.append("Good coverage of core functionality")

        # Identify potential issues
        issues = []
        if avg_relevance < 50:
            issues.append("Low average relevance score may indicate API design issues")
        if complexity_counts.get('complex', 0) > total_endpoints * 0.3:
            issues.append("High number of complex endpoints may impact usability")
        if len([ep for ep in endpoints if not ep.description]) > total_endpoints * 0.5:
            issues.append("Many endpoints lack proper documentation")

        # Suggest improvements
        improvements = []
        if not strengths:
            improvements.append("Consider improving endpoint documentation and design")
        improvements.append("Focus on high-relevance endpoints for initial implementation")
        improvements.append("Group related endpoints for better organization")

        # Best practices
        best_practices = [
            "Start with core functionality endpoints",
            "Implement proper error handling for all tools",
            "Add authentication if required by the API",
            "Test generated tools thoroughly before deployment"
        ]

        return AIRecommendation(
            summary=summary,
            strengths=strengths or ["API structure is analyzable"],
            potential_issues=issues or ["No major issues identified"],
            suggested_improvements=improvements,
            best_practices=best_practices
        )

    def _calculate_relevance_distribution(self, endpoints: List[OpenAPIEndpoint]) -> RelevanceDistribution:
        """Calculate distribution of endpoints by relevance score."""
        high = len([ep for ep in endpoints if ep.relevance_score >= 80])
        medium = len([ep for ep in endpoints if 50 <= ep.relevance_score < 80])
        low = len([ep for ep in endpoints if ep.relevance_score < 50])

        return RelevanceDistribution(high=high, medium=medium, low=low)

    def _determine_overall_complexity(self, endpoints: List[OpenAPIEndpoint]) -> str:
        """Determine overall API complexity."""
        if not endpoints:
            return 'simple'

        complexity_scores = {'simple': 1, 'moderate': 2, 'complex': 3}
        avg_complexity = sum(complexity_scores[ep.complexity_level] for ep in endpoints) / len(endpoints)

        if avg_complexity <= 1.5:
            return 'simple'
        elif avg_complexity <= 2.5:
            return 'moderate'
        else:
            return 'complex'
