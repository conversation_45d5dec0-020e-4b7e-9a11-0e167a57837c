"""
MCP server code generation service.
"""

import os
import tempfile
import zipfile
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List

import structlog
from jinja2 import Environment, FileSystemLoader, Template

from ..config import settings
from ..models.analysis import OpenAPIEndpoint
from ..models.generation import (
    FileType,
    GeneratedFile,
    GenerationConfig,
    GenerationMetadata,
    TransportType,
)

logger = structlog.get_logger(__name__)


class CodeGenerator:
    """Generates Python FastMCP 2.0 server code from curated OpenAPI tools."""
    
    def __init__(self):
        # Setup Jinja2 environment
        template_dir = Path(__file__).parent.parent.parent.parent / "templates"
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(template_dir)),
            trim_blocks=True,
            lstrip_blocks=True,
        )
        
        # Add custom filters
        self.jinja_env.filters['tojsonfilter'] = self._to_json_filter

    def _to_json_filter(self, value):
        """Custom Jinja2 filter to convert value to JSON string."""
        import json
        if isinstance(value, str):
            return json.dumps(value)
        elif isinstance(value, (list, dict)):
            return json.dumps(value)
        else:
            return json.dumps(str(value))
    
    async def generate_mcp_server(
        self,
        endpoints: List[OpenAPIEndpoint],
        servers: List[str],
        config: GenerationConfig
    ) -> Dict:
        """Generate a complete MCP server from curated endpoints."""
        # Validate inputs
        if not endpoints:
            raise ValueError("No endpoints provided for generation")

        if not servers:
            logger.warning("No servers provided, using default")
            servers = ["https://api.example.com"]

        # Ensure servers is not empty for template safety
        if not servers or len(servers) == 0:
            servers = ["https://api.example.com"]

        logger.info(
            "Starting MCP server generation",
            server_name=config.server_name,
            endpoint_count=len(endpoints),
            server_count=len(servers),
            transport=config.transport
        )
        
        # Prepare template context with validation
        context = {
            "config": config,
            "endpoints": endpoints,
            "servers": servers if servers else ["https://api.example.com"],
            "generation_timestamp": datetime.now(timezone.utc).isoformat(),
        }

        # Validate context for template safety
        if not context["servers"]:
            context["servers"] = ["https://api.example.com"]
        if not context["endpoints"]:
            raise ValueError("No endpoints available for template generation")
        
        # Generate files
        generated_files = []

        # Main Python server file
        server_file = await self._generate_python_server(context)
        generated_files.append(server_file)

        # Python dependency files
        if config.use_poetry:
            pyproject_file = await self._generate_pyproject_toml(context)
            generated_files.append(pyproject_file)

        requirements_file = await self._generate_requirements_txt(context)
        generated_files.append(requirements_file)

        # Generate metadata first (needed for README)
        metadata = GenerationMetadata(
            generator="caylex-mcp-generator",
            tool_count=len(endpoints),
            transport=config.transport,
            next_steps=self._generate_next_steps(config),
            dependencies=self._get_dependencies(config),
            environment_variables=self._get_environment_variables(config),
        )

        # Add metadata to context for README generation
        context["metadata"] = metadata

        # README.md
        readme_file = await self._generate_readme(context)
        generated_files.append(readme_file)

        # Environment file template
        env_file = await self._generate_env_template(context)
        generated_files.append(env_file)

        # Docker files (optional)
        if config.include_docker:
            dockerfile = await self._generate_dockerfile(context)
            generated_files.append(dockerfile)

            docker_compose = await self._generate_docker_compose(context)
            generated_files.append(docker_compose)
        
        # Create ZIP file
        zip_path = await self._create_zip_file(generated_files, config.server_name)
        
        result = {
            "generated_files": generated_files,
            "metadata": metadata,
            "zip_path": zip_path,
            "config": config,
        }
        
        logger.info(
            "Completed MCP server generation",
            server_name=config.server_name,
            file_count=len(generated_files),
            zip_path=zip_path
        )
        
        return result
    
    async def _generate_python_server(self, context: Dict) -> GeneratedFile:
        """Generate Python server file."""
        try:
            template = self.jinja_env.get_template("fastmcp_server.py.j2")
            content = template.render(**context)

            return GeneratedFile(
                name="server.py",
                type=FileType.PYTHON,
                path="server.py",
                content=content,
                size=len(content.encode('utf-8'))
            )
        except Exception as e:
            logger.error("Failed to generate Python server", error=str(e), context_keys=list(context.keys()))
            raise ValueError(f"Python server generation failed: {str(e)}")

    async def _generate_pyproject_toml(self, context: Dict) -> GeneratedFile:
        """Generate pyproject.toml file."""
        try:
            template = self.jinja_env.get_template("pyproject.toml.j2")
            content = template.render(**context)

            return GeneratedFile(
                name="pyproject.toml",
                type=FileType.TOML,
                path="pyproject.toml",
                content=content,
                size=len(content.encode('utf-8'))
            )
        except Exception as e:
            logger.error("Failed to generate pyproject.toml", error=str(e), context_keys=list(context.keys()))
            raise ValueError(f"pyproject.toml generation failed: {str(e)}")

    async def _generate_requirements_txt(self, context: Dict) -> GeneratedFile:
        """Generate requirements.txt file."""
        try:
            template = self.jinja_env.get_template("requirements.txt.j2")
            content = template.render(**context)

            return GeneratedFile(
                name="requirements.txt",
                type=FileType.TEXT,
                path="requirements.txt",
                content=content,
                size=len(content.encode('utf-8'))
            )
        except Exception as e:
            logger.error("Failed to generate requirements.txt", error=str(e), context_keys=list(context.keys()))
            raise ValueError(f"requirements.txt generation failed: {str(e)}")
    
    async def _generate_readme(self, context: Dict) -> GeneratedFile:
        """Generate README.md file."""
        try:
            template = self.jinja_env.get_template("README.md.j2")
            content = template.render(**context)

            return GeneratedFile(
                name="README.md",
                type=FileType.MARKDOWN,
                path="README.md",
                content=content,
                size=len(content.encode('utf-8'))
            )
        except Exception as e:
            logger.error("Failed to generate README", error=str(e), context_keys=list(context.keys()))
            raise ValueError(f"README generation failed: {str(e)}")
    
    async def _generate_env_template(self, context: Dict) -> GeneratedFile:
        """Generate environment template file."""
        config = context["config"]

        env_lines = [
            "# {{ config.name }} - Environment Configuration",
            "# Generated by Caylex MCP Generator",
            "",
            "# API Configuration",
            "API_KEY=your_api_key_here",
            "",
        ]

        if config.include_auth:
            env_lines.extend([
                "# Authentication",
                "API_TOKEN=your_bearer_token_here",
                "",
            ])

        if config.transport == TransportType.STREAMABLE_HTTP:
            env_lines.extend([
                "# Server Configuration",
                "HOST=0.0.0.0",
                f"PORT={config.port}",
                "",
            ])

        if config.include_logging:
            env_lines.extend([
                "# Logging Configuration",
                "LOG_LEVEL=info",
                "PYTHONUNBUFFERED=1",
                "",
            ])

        if config.include_cors:
            env_lines.extend([
                "# CORS Configuration",
                "CORS_ORIGIN=*",
                "",
            ])

        # Add Python-specific environment variables
        env_lines.extend([
            "# Python Configuration",
            "PYTHONPATH=.",
            "",
        ])

        content = "\n".join(env_lines)

        return GeneratedFile(
            name=".env.example",
            type=FileType.TEXT,
            path=".env.example",
            content=content,
            size=len(content.encode('utf-8'))
        )

    async def _generate_dockerfile(self, context: Dict) -> GeneratedFile:
        """Generate Dockerfile."""
        try:
            template = self.jinja_env.get_template("Dockerfile.j2")
            content = template.render(**context)

            return GeneratedFile(
                name="Dockerfile",
                type=FileType.DOCKERFILE,
                path="Dockerfile",
                content=content,
                size=len(content.encode('utf-8'))
            )
        except Exception as e:
            logger.error("Failed to generate Dockerfile", error=str(e), context_keys=list(context.keys()))
            raise ValueError(f"Dockerfile generation failed: {str(e)}")

    async def _generate_docker_compose(self, context: Dict) -> GeneratedFile:
        """Generate docker-compose.yml."""
        try:
            template = self.jinja_env.get_template("docker-compose.yml.j2")
            content = template.render(**context)

            return GeneratedFile(
                name="docker-compose.yml",
                type=FileType.YAML,
                path="docker-compose.yml",
                content=content,
                size=len(content.encode('utf-8'))
            )
        except Exception as e:
            logger.error("Failed to generate docker-compose.yml", error=str(e), context_keys=list(context.keys()))
            raise ValueError(f"docker-compose.yml generation failed: {str(e)}")

    async def _create_zip_file(self, files: List[GeneratedFile], server_name: str) -> str:
        """Create a ZIP file containing all generated files."""
        # Create temporary directory
        temp_dir = tempfile.mkdtemp()
        zip_path = os.path.join(temp_dir, f"{server_name}.zip")

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file in files:
                # Create directory structure in ZIP
                arcname = f"{server_name}/{file.path}"
                zipf.writestr(arcname, file.content)

        logger.info("Created ZIP file", zip_path=zip_path, file_count=len(files))
        return zip_path

    def _generate_next_steps(self, config: GenerationConfig) -> List[str]:
        """Generate next steps for deployment."""
        steps = [
            "Extract the ZIP file to your desired location",
            f"Install Python {config.python_version} or higher",
        ]

        if config.use_poetry:
            steps.extend([
                "Install Poetry: curl -sSL https://install.python-poetry.org | python3 -",
                "Install dependencies with 'poetry install'",
            ])
        else:
            steps.extend([
                "Create virtual environment: python -m venv venv",
                "Activate virtual environment: source venv/bin/activate (Linux/Mac) or venv\\Scripts\\activate (Windows)",
                "Install dependencies with 'pip install -r requirements.txt'",
            ])

        steps.extend([
            "Configure environment variables in .env file",
            "Set your API key and other required credentials",
        ])

        if config.transport == TransportType.STREAMABLE_HTTP:
            if config.use_poetry:
                steps.append(f"Start the server with 'poetry run python server.py' (will run on port {config.port})")
            else:
                steps.append(f"Start the server with 'python server.py' (will run on port {config.port})")
        else:
            if config.use_poetry:
                steps.append("Run the server with 'poetry run python server.py'")
            else:
                steps.append("Run the server with 'python server.py'")

        steps.extend([
            "Test the server with your MCP client",
            "Deploy to your preferred hosting platform",
        ])

        return steps

    def _get_dependencies(self, config: GenerationConfig) -> List[str]:
        """Get list of required dependencies."""
        deps = [
            f"Python {config.python_version}+",
            "fastmcp>=2.0.0",
            "httpx>=0.25.0"
        ]

        if config.include_logging:
            deps.append("structlog>=23.0.0")

        if config.transport == TransportType.STREAMABLE_HTTP:
            deps.append("uvicorn>=0.24.0")

        if config.include_auth:
            deps.append("pyjwt>=2.8.0")

        if config.use_poetry:
            deps.append("poetry (for dependency management)")

        return deps

    def _get_environment_variables(self, config: GenerationConfig) -> Dict[str, str]:
        """Get required environment variables."""
        env_vars = {
            "API_KEY": "Your API key for authentication",
            "PYTHONPATH": "Python path (default: .)",
            "PYTHONUNBUFFERED": "Disable Python output buffering (default: 1)",
        }

        if config.include_auth:
            env_vars["API_TOKEN"] = "Bearer token for MCP authentication"

        if config.transport == TransportType.STREAMABLE_HTTP:
            env_vars["HOST"] = "Server host (default: 0.0.0.0)"
            env_vars["PORT"] = f"Server port (default: {config.port})"

        if config.include_logging:
            env_vars["LOG_LEVEL"] = "Logging level (default: info)"

        if config.include_cors:
            env_vars["CORS_ORIGIN"] = "CORS origin (default: *)"

        return env_vars
