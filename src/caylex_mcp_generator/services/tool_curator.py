"""
Tool curation service for filtering and customizing OpenAPI tools.
"""

import json
import re
from typing import Dict, List, Set

import structlog

from ..models.analysis import AnalysisResult, OpenAPIEndpoint
from ..models.curation import CurationRequest, RiskLevel, TemplateType, ToolCustomization

logger = structlog.get_logger(__name__)


class ToolCurator:
    """Curates and filters tools based on user preferences."""
    
    def __init__(self):
        self.risk_filters = {
            RiskLevel.LOW: self._low_risk_filter,
            RiskLevel.MEDIUM: self._medium_risk_filter,
            RiskLevel.HIGH: self._high_risk_filter,
        }
        
        self.template_filters = {
            TemplateType.BASIC_CRUD: self._basic_crud_filter,
            TemplateType.FULL_API: self._full_api_filter,
            TemplateType.READ_ONLY: self._read_only_filter,
            TemplateType.ADMIN_TOOLS: self._admin_tools_filter,
            TemplateType.CORE_FEATURES: self._core_features_filter,
            TemplateType.CUSTOM: self._custom_filter,
        }
    
    async def curate_tools(self, analysis_result: AnalysisResult, curation_request: CurationRequest) -> Dict:
        """Curate tools based on the curation request."""
        logger.info(
            "Starting tool curation",
            session_id=curation_request.session_id,
            template_type=curation_request.template_type,
            selected_tools_count=len(curation_request.selected_tools),
            risk_level=curation_request.customizations.risk_level,
            available_endpoints=len(analysis_result.endpoints)
        )
        
        # Start with all endpoints
        available_endpoints = {ep.operation_id: ep for ep in analysis_result.endpoints}
        
        # Dump available endpoints to file for debugging
        with open('available_endpoints.json', 'w') as f:
            json.dump({k: ep.dict() for k, ep in available_endpoints.items()}, f, indent=2)

        # Dump curation request to file for debugging
        with open('curation_request.json', 'w') as f:
            json.dump(curation_request.dict(), f, indent=2)

        logger.info(
            "Applying user selection filter",
            session_id=curation_request.session_id,
            selected_tools=curation_request.selected_tools
        )
        
        # Apply user selection filter
        if curation_request.selected_tools:
            selected_endpoints = {
                op_id: ep for op_id, ep in available_endpoints.items()
                if op_id in curation_request.selected_tools
            }
        else:
            selected_endpoints = available_endpoints.copy()
        
        # Dump selected endpoints to file for debugging
        with open('selected_endpoints.json', 'w') as f:
            json.dump({k: ep.dict() for k, ep in selected_endpoints.items()}, f, indent=2)

        logger.info(
            "Selected endpoints after user selection",
            session_id=curation_request.session_id,
            endpoints=selected_endpoints
        )

        # Apply template filter
        template_filtered = await self._apply_template_filter(
            selected_endpoints, 
            curation_request.template_type,
            analysis_result
        )

        # Dump template filtered endpoints to file for debugging
        with open('template_filtered_endpoints.json', 'w') as f:
            json.dump({k: ep.dict() for k, ep in template_filtered.items()}, f, indent=2)
        
        # Apply risk level filter
        risk_filtered = await self._apply_risk_filter(
            template_filtered,
            curation_request.customizations.risk_level
        )

        # Dump risk filtered endpoints to file for debugging
        with open('risk_filtered_endpoints.json', 'w') as f:
            json.dump({k: ep.dict() for k, ep in risk_filtered.items()}, f, indent=2)
        
        # Apply customization filters
        final_endpoints = await self._apply_customization_filters(
            risk_filtered,
            curation_request.customizations
        )

        # Dump final endpoints to file for debugging
        with open('final_endpoints.json', 'w') as f:
            json.dump({k: ep.dict() for k, ep in final_endpoints.items()}, f, indent=2)
        
        # Generate curation metadata
        applied_filters = self._get_applied_filters(curation_request)
        
        curation_data = {
            "curated_endpoints": list(final_endpoints.values()),
            "original_count": len(analysis_result.endpoints),
            "selected_count": len(curation_request.selected_tools) if curation_request.selected_tools else len(analysis_result.endpoints),
            "template_filtered_count": len(template_filtered),
            "risk_filtered_count": len(risk_filtered),
            "final_count": len(final_endpoints),
            "applied_filters": applied_filters,
            "template_type": curation_request.template_type,
            "customizations": curation_request.customizations.dict(),
        }
        
        logger.info(
            "Completed tool curation",
            session_id=curation_request.session_id,
            original_count=curation_data["original_count"],
            selected_count=curation_data["selected_count"],
            template_filtered_count=curation_data["template_filtered_count"],
            risk_filtered_count=curation_data["risk_filtered_count"],
            final_count=curation_data["final_count"],
            applied_filters=applied_filters
        )
        
        return curation_data
    
    async def _apply_template_filter(
        self, 
        endpoints: Dict[str, OpenAPIEndpoint], 
        template_type: TemplateType,
        analysis_result: AnalysisResult
    ) -> Dict[str, OpenAPIEndpoint]:
        """Apply template-based filtering."""
        filter_func = self.template_filters.get(template_type, self._custom_filter)
        return await filter_func(endpoints, analysis_result)
    
    async def _apply_risk_filter(
        self, 
        endpoints: Dict[str, OpenAPIEndpoint], 
        risk_level: RiskLevel
    ) -> Dict[str, OpenAPIEndpoint]:
        """Apply risk-based filtering."""
        filter_func = self.risk_filters.get(risk_level, self._medium_risk_filter)
        return await filter_func(endpoints)
    
    async def _apply_customization_filters(
        self, 
        endpoints: Dict[str, OpenAPIEndpoint], 
        customizations: ToolCustomization
    ) -> Dict[str, OpenAPIEndpoint]:
        """Apply customization-based filters."""
        filtered_endpoints = endpoints.copy()
        
        # Apply max tools limit
        if customizations.max_tools and len(filtered_endpoints) > customizations.max_tools:
            # Sort by relevance score and take top N
            sorted_endpoints = sorted(
                filtered_endpoints.values(), 
                key=lambda x: x.relevance_score, 
                reverse=True
            )
            filtered_endpoints = {
                ep.operation_id: ep for ep in sorted_endpoints[:customizations.max_tools]
            }
        
        # Apply exclude patterns
        if customizations.exclude_patterns:
            for pattern in customizations.exclude_patterns:
                filtered_endpoints = {
                    op_id: ep for op_id, ep in filtered_endpoints.items()
                    if not self._matches_pattern(ep, pattern)
                }
        
        # Apply include patterns (if specified, only keep matching endpoints)
        if customizations.include_patterns:
            include_matches = set()
            for pattern in customizations.include_patterns:
                for op_id, ep in filtered_endpoints.items():
                    if self._matches_pattern(ep, pattern):
                        include_matches.add(op_id)
            
            if include_matches:
                filtered_endpoints = {
                    op_id: ep for op_id, ep in filtered_endpoints.items()
                    if op_id in include_matches
                }
        
        return filtered_endpoints
    
    def _matches_pattern(self, endpoint: OpenAPIEndpoint, pattern: str) -> bool:
        """Check if endpoint matches a pattern."""
        # Check operation ID, path, and summary
        text_to_check = f"{endpoint.operation_id} {endpoint.path} {endpoint.summary}".lower()
        
        # Support both regex and simple string matching
        try:
            return bool(re.search(pattern.lower(), text_to_check))
        except re.error:
            # Fall back to simple string matching if regex is invalid
            return pattern.lower() in text_to_check
    
    # Template filters
    async def _basic_crud_filter(self, endpoints: Dict[str, OpenAPIEndpoint], analysis_result: AnalysisResult) -> Dict[str, OpenAPIEndpoint]:
        """Filter for basic CRUD operations."""
        return {
            op_id: ep for op_id, ep in endpoints.items()
            if ep.functionality_type == 'core' and ep.complexity_level in ['simple', 'moderate']
        }
    
    async def _full_api_filter(self, endpoints: Dict[str, OpenAPIEndpoint], analysis_result: AnalysisResult) -> Dict[str, OpenAPIEndpoint]:
        """Filter for full API (all endpoints)."""
        return endpoints.copy()
    
    async def _read_only_filter(self, endpoints: Dict[str, OpenAPIEndpoint], analysis_result: AnalysisResult) -> Dict[str, OpenAPIEndpoint]:
        """Filter for read-only operations."""
        return {
            op_id: ep for op_id, ep in endpoints.items()
            if ep.method == 'GET'
        }
    
    async def _admin_tools_filter(self, endpoints: Dict[str, OpenAPIEndpoint], analysis_result: AnalysisResult) -> Dict[str, OpenAPIEndpoint]:
        """Filter for admin tools."""
        return {
            op_id: ep for op_id, ep in endpoints.items()
            if ep.functionality_type == 'admin' or 'admin' in ep.workflow_tags
        }
    
    async def _core_features_filter(self, endpoints: Dict[str, OpenAPIEndpoint], analysis_result: AnalysisResult) -> Dict[str, OpenAPIEndpoint]:
        """Filter for core features only."""
        return {
            op_id: ep for op_id, ep in endpoints.items()
            if ep.functionality_type == 'core' and ep.relevance_score >= 70
        }
    
    async def _custom_filter(self, endpoints: Dict[str, OpenAPIEndpoint], analysis_result: AnalysisResult) -> Dict[str, OpenAPIEndpoint]:
        """Custom filter (no filtering)."""
        return endpoints.copy()
    
    # Risk filters
    async def _low_risk_filter(self, endpoints: Dict[str, OpenAPIEndpoint]) -> Dict[str, OpenAPIEndpoint]:
        """Filter for low-risk operations only."""
        return {
            op_id: ep for op_id, ep in endpoints.items()
            if ep.method == 'GET' and ep.functionality_type not in ['admin']
        }
    
    async def _medium_risk_filter(self, endpoints: Dict[str, OpenAPIEndpoint]) -> Dict[str, OpenAPIEndpoint]:
        """Filter for low to medium risk operations."""
        return {
            op_id: ep for op_id, ep in endpoints.items()
            if ep.functionality_type not in ['admin'] or ep.method in ['GET', 'POST']
        }
    
    async def _high_risk_filter(self, endpoints: Dict[str, OpenAPIEndpoint]) -> Dict[str, OpenAPIEndpoint]:
        """Allow all operations (high risk tolerance)."""
        return endpoints.copy()
    
    def _get_applied_filters(self, curation_request: CurationRequest) -> List[str]:
        """Get list of filters that were applied."""
        filters = []
        
        if curation_request.selected_tools:
            filters.append(f"user_selection ({len(curation_request.selected_tools)} tools)")
        
        filters.append(f"template_{curation_request.template_type.value}")
        filters.append(f"risk_{curation_request.customizations.risk_level.value}")
        
        if curation_request.customizations.max_tools:
            filters.append(f"max_tools_{curation_request.customizations.max_tools}")
        
        if curation_request.customizations.exclude_patterns:
            filters.append(f"exclude_patterns ({len(curation_request.customizations.exclude_patterns)})")
        
        if curation_request.customizations.include_patterns:
            filters.append(f"include_patterns ({len(curation_request.customizations.include_patterns)})")
        
        return filters
