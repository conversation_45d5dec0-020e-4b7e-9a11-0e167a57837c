"""
Enhanced analyzer service that integrates PatternAnalyzer and LLMAnalyzer.
"""

import asyncio
from typing import Dict, List, Any, Optional
import logging

from ..config import settings
from ..analysis.pattern_analyzer import PatternAnalyzer
from ..analysis.llm_analyzer import LLMAnalyzer
from ..models.analysis import (
    EnhancedAnalysis,
    WorkflowGroup,
    SelectionTemplate,
    AIRecommendation,
    RelevanceDistribution,
    OpenAPIEndpoint
)

logger = logging.getLogger(__name__)


class EnhancedAnalyzer:
    """
    Enhanced analyzer that combines pattern-based and LLM-based analysis.
    
    This service integrates the PatternAnalyzer and LLMAnalyzer to provide
    comprehensive tool analysis with proper fallback handling.
    """
    
    def __init__(self):
        """Initialize the enhanced analyzer."""
        self.pattern_analyzer = PatternAnalyzer()
        self.llm_analyzer = None
        
        # Initialize LLM analyzer if API key is available
        if settings.anthropic_api_key:
            try:
                self.llm_analyzer = LLMAnalyzer({
                    "api_key": settings.anthropic_api_key,
                    "model": settings.anthropic_model,
                    "timeout_ms": settings.anthropic_timeout_ms,
                    "max_retries": settings.anthropic_max_retries
                })
                logger.info("✅ LLM analyzer initialized with Anthropic API")
            except Exception as e:
                logger.warning(f"⚠️ Failed to initialize LLM analyzer: {str(e)}")
                self.llm_analyzer = None
        else:
            logger.info("ℹ️ No Anthropic API key configured, using pattern-only analysis")

    async def perform_enhanced_analysis(self, endpoints: List[OpenAPIEndpoint], 
                                      spec: Dict[str, Any]) -> EnhancedAnalysis:
        """
        Perform enhanced analysis using both pattern and LLM analyzers.
        
        Args:
            endpoints: List of analyzed endpoints
            spec: Original OpenAPI specification
            
        Returns:
            Enhanced analysis results
        """
        logger.info(f"🔍 Starting enhanced analysis for {len(endpoints)} endpoints")
        
        # Convert endpoints to tool format for pattern analysis
        tools = self._convert_endpoints_to_tools(endpoints)
        
        # Step 1: Run pattern analysis
        logger.info("📊 Running pattern analysis...")
        pattern_results = self.pattern_analyzer.analyze(tools)
        
        # Step 2: Enhance with LLM analysis if available
        if self.llm_analyzer:
            logger.info("🤖 Enhancing with LLM analysis...")
            try:
                # Define enhancement context
                context = {
                    "purpose": "general use",
                    "user_type": "developer", 
                    "risk_tolerance": "medium"
                }
                
                # Run LLM enhancement
                enhanced_results = await self.llm_analyzer.enhance_analysis(
                    pattern_results, 
                    context
                )
                
                # Convert LLM results to expected format
                return self._convert_llm_results_to_enhanced_analysis(
                    enhanced_results, endpoints
                )
                
            except Exception as e:
                logger.error(f"🚨 LLM analysis failed, falling back to pattern-only: {str(e)}")
                return self._create_pattern_only_enhanced_analysis(
                    pattern_results, endpoints, spec
                )
        else:
            logger.info("📋 Using pattern-only analysis")
            return self._create_pattern_only_enhanced_analysis(
                pattern_results, endpoints, spec
            )

    def _convert_endpoints_to_tools(self, endpoints: List[OpenAPIEndpoint]) -> List[Dict[str, Any]]:
        """
        Convert OpenAPIEndpoint objects to tool format for pattern analysis.
        
        Args:
            endpoints: List of OpenAPIEndpoint objects
            
        Returns:
            List of tools in the format expected by PatternAnalyzer
        """
        tools = []
        for endpoint in endpoints:
            tool = {
                "name": endpoint.operation_id,
                "operationId": endpoint.operation_id,
                "method": endpoint.method,
                "path": endpoint.path,
                "pathTemplate": endpoint.path,
                "summary": endpoint.summary,
                "description": endpoint.description,
                "parameters": endpoint.parameters,
                "tags": getattr(endpoint, 'tags', [])
            }
            
            # Add request body info if available from original_tool
            if hasattr(endpoint, 'original_tool') and endpoint.original_tool:
                original = endpoint.original_tool
                if "operation" in original:
                    operation = original["operation"]
                    tool["requestBody"] = operation.get("requestBody")
                    tool["securityRequirements"] = operation.get("security", [])
            
            tools.append(tool)
        
        return tools

    def _convert_llm_results_to_enhanced_analysis(self, llm_results: Dict[str, Any], 
                                                endpoints: List[OpenAPIEndpoint]) -> EnhancedAnalysis:
        """
        Convert LLM analysis results to EnhancedAnalysis format.
        
        Args:
            llm_results: Results from LLM analyzer
            endpoints: Original endpoints for reference
            
        Returns:
            EnhancedAnalysis object
        """
        # Convert workflow groups
        workflow_groups = {}
        for key, workflow in llm_results.get("workflow_groups", {}).items():
            workflow_groups[key] = WorkflowGroup(
                name=workflow.get("name", key),
                description=workflow.get("description", ""),
                tools=workflow.get("tools", []),
                priority=self._convert_priority_to_int(workflow.get("priority", "medium"))
            )
        
        # Convert selection templates
        selection_templates = {}
        for key, template in llm_results.get("selection_templates", {}).items():
            selection_templates[key] = SelectionTemplate(
                name=template.get("name", key),
                description=template.get("description", ""),
                recommended_tools=template.get("selected_tools", []),
                use_case=template.get("reasoning", "General use case")
            )
        
        # Convert recommendations
        recommendations_data = llm_results.get("recommendations", {})
        recommendations = AIRecommendation(
            summary=recommendations_data.get("reasoning", "AI-enhanced analysis completed"),
            strengths=["Enhanced with LLM analysis", "Comprehensive workflow grouping"],
            potential_issues=recommendations_data.get("complexity_warnings", []),
            suggested_improvements=["Review high-priority workflows first"],
            best_practices=[
                "Start with highest-value tools",
                "Follow workflow-based implementation order",
                "Test tools in recommended templates"
            ]
        )
        
        # Get high relevance tools
        high_relevance_tools = []
        for tool in llm_results.get("tools", []):
            if tool.get("relevance_score", 0) >= 80:
                # Map back to operation_id
                tool_name = tool.get("name", "")
                matching_endpoint = next(
                    (ep for ep in endpoints if ep.operation_id == tool_name),
                    None
                )
                if matching_endpoint:
                    high_relevance_tools.append(matching_endpoint.operation_id)
        
        # Calculate relevance distribution
        relevance_distribution = self._calculate_relevance_distribution_from_llm(
            llm_results.get("tools", [])
        )
        
        return EnhancedAnalysis(
            workflow_groups=workflow_groups,
            selection_templates=selection_templates,
            recommendations=recommendations,
            high_relevance_tools=high_relevance_tools,
            relevance_distribution=relevance_distribution
        )

    def _create_pattern_only_enhanced_analysis(self, pattern_results: Dict[str, Any], 
                                             endpoints: List[OpenAPIEndpoint],
                                             spec: Dict[str, Any]) -> EnhancedAnalysis:
        """
        Create enhanced analysis using only pattern analysis results.
        
        Args:
            pattern_results: Results from pattern analyzer
            endpoints: Original endpoints
            spec: OpenAPI specification
            
        Returns:
            EnhancedAnalysis object based on pattern analysis
        """
        # Create basic workflow groups based on functionality types
        workflow_groups = {}
        func_type_groups = {}
        
        for endpoint in endpoints:
            func_type = endpoint.functionality_type
            if func_type not in func_type_groups:
                func_type_groups[func_type] = []
            func_type_groups[func_type].append(endpoint.operation_id)
        
        for func_type, tools in func_type_groups.items():
            if tools:  # Only create groups with tools
                workflow_groups[func_type] = WorkflowGroup(
                    name=func_type.replace('_', ' ').title(),
                    description=f"{func_type.replace('_', ' ').title()} functionality endpoints",
                    tools=tools,
                    priority=self._get_priority_for_func_type(func_type)
                )
        
        # Create basic selection templates
        selection_templates = {}
        
        # Essential template - high relevance tools
        high_relevance_tools = [ep.operation_id for ep in endpoints if ep.relevance_score >= 80]
        if high_relevance_tools:
            selection_templates["essential"] = SelectionTemplate(
                name="Essential Tools",
                description="High-relevance tools for core functionality",
                recommended_tools=high_relevance_tools,
                use_case="Core API functionality and most common operations"
            )
        
        # Core template - core functionality
        core_tools = [ep.operation_id for ep in endpoints if ep.functionality_type == "core"]
        if core_tools:
            selection_templates["core"] = SelectionTemplate(
                name="Core Functionality",
                description="Core business logic endpoints",
                recommended_tools=core_tools,
                use_case="Primary business operations and data management"
            )
        
        # Create basic recommendations
        avg_relevance = sum(ep.relevance_score for ep in endpoints) / len(endpoints) if endpoints else 0
        
        recommendations = AIRecommendation(
            summary=f"Pattern-based analysis of {len(endpoints)} endpoints with average relevance {avg_relevance:.1f}",
            strengths=[
                "Comprehensive pattern-based safety analysis",
                "Systematic endpoint categorization",
                "Relevance scoring for prioritization"
            ],
            potential_issues=[
                "Limited semantic understanding without LLM analysis",
                "May miss complex workflow relationships"
            ],
            suggested_improvements=[
                "Configure Anthropic API key for enhanced LLM analysis",
                "Focus on high-relevance endpoints first",
                "Review safety flags for dangerous operations"
            ],
            best_practices=[
                "Start with essential tools template",
                "Implement proper authentication handling",
                "Test all tools thoroughly before deployment",
                "Monitor for rate limiting and errors"
            ]
        )
        
        # Calculate relevance distribution
        relevance_distribution = RelevanceDistribution(
            high=len([ep for ep in endpoints if ep.relevance_score >= 80]),
            medium=len([ep for ep in endpoints if 50 <= ep.relevance_score < 80]),
            low=len([ep for ep in endpoints if ep.relevance_score < 50])
        )
        
        return EnhancedAnalysis(
            workflow_groups=workflow_groups,
            selection_templates=selection_templates,
            recommendations=recommendations,
            high_relevance_tools=high_relevance_tools,
            relevance_distribution=relevance_distribution
        )

    def _convert_priority_to_int(self, priority_str: str) -> int:
        """Convert priority string to integer."""
        priority_map = {"high": 8, "medium": 5, "low": 2}
        return priority_map.get(priority_str.lower(), 5)

    def _get_priority_for_func_type(self, func_type: str) -> int:
        """Get priority for functionality type."""
        priority_map = {
            "core": 9,
            "auth": 8,
            "utility": 6,
            "monitoring": 4,
            "admin": 3,
            "edge-case": 2,
            "system": 1
        }
        return priority_map.get(func_type, 5)

    def _calculate_relevance_distribution_from_llm(self, tools: List[Dict[str, Any]]) -> RelevanceDistribution:
        """Calculate relevance distribution from LLM results."""
        high = len([t for t in tools if t.get("relevance_score", 0) >= 80])
        medium = len([t for t in tools if 50 <= t.get("relevance_score", 0) < 80])
        low = len([t for t in tools if t.get("relevance_score", 0) < 50])
        
        return RelevanceDistribution(high=high, medium=medium, low=low)
