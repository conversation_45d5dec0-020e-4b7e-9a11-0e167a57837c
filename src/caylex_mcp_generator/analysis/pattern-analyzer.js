"""
Enhanced Pattern-Based Tool Analyzer for AI Agent Relevance

Focuses on reliable detection of:
- Safety signals (danger patterns, HTTP method risk)
- Authentication requirements
- Input schema extraction
- Endpoint metadata for LLM analysis
"""

import re
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)


class PatternAnalyzer:
  constructor() {
    // Danger patterns we can reliably detect
    this.dangerPatterns = {
      ADMIN_OPERATIONS: [
          /\/admin(?:\/|$)/i,
          /\/manage(?:\/|$)/i,
        /\/system(?:\/|$)/i,
        /\/config(?:\/|$)/i,
        /\/settings(?:\/|$)/i
      ],
      
      BULK_OPERATIONS: [
        /(?:delete|update|create|modify).*(?:all|many|bulk)/i,
        /(?:all|many|bulk).*(?:delete|update|create|modify)/i,
        /batch.*(?:delete|update|create|modify)/i,
        /.*(?:all|many|bulk)$/i
      ],
      
      FINANCIAL_OPERATIONS: [
          /\/payment(?:s)?(?:\/|$)/i,
          /\/billing(?:\/|$)/i,
        /\/treasury(?:\/|$)/i,
        /\/transaction(?:s)?(?:\/|$)/i,
        /\/charge(?:s)?(?:\/|$)/i,
        /\/refund(?:s)?(?:\/|$)/i,
          /\/invoice(?:s)?(?:\/|$)/i,
          /\/subscription(?:s)?(?:\/|$)/i
      ],
      
      ORGANIZATION_OPERATIONS: [
        /\/org(?:anization)?s?(?:\/|$)/i,
        /\/team(?:s)?(?:\/|$)/i,
        /\/workspace(?:s)?(?:\/|$)/i,
        /\/tenant(?:s)?(?:\/|$)/i
      ],
      
      INTEGRATION_OPERATIONS: [
        /\/webhook(?:s)?(?:\/|$)/i,
        /\/integration(?:s)?(?:\/|$)/i,
        /\/callback(?:s)?(?:\/|$)/i,
        /\/api[_-]?key(?:s)?(?:\/|$)/i
      ],
      
      SYSTEM_OPERATIONS: [
        /\/migration(?:s)?(?:\/|$)/i,
        /\/backup(?:s)?(?:\/|$)/i,
        /\/export(?:s)?(?:\/|$)/i,
        /\/import(?:s)?(?:\/|$)/i,
        /\/sync(?:\/|$)/i,
        /\/deploy(?:\/|$)/i
      ]
    };

    // HTTP method risk levels
    this.methodRisk = {
      'GET': 'LOW',
      'POST': 'MEDIUM', 
      'PUT': 'MEDIUM',
      'PATCH': 'MEDIUM',
      'DELETE': 'HIGH'
    };
  }
  
  /**
   * Main analysis function - simplified and focused
   */
  analyze(tools) {
    console.log(`🔍 Analyzing ${tools.length} tools for AI agent relevance...`);
    
    const processedTools = [];
    const safetyStats = { safe: 0, forbidden: 0, risky: 0 };
    
    tools.forEach((tool, index) => {
      const analysis = this._analyzeToolForAgent(tool, `tool_${index}`);
      processedTools.push(analysis);
      
      // Update safety stats
      if (analysis.safety_flags.is_forbidden) {
        safetyStats.forbidden++;
      } else if (analysis.safety_flags.danger_keywords.length > 0) {
        safetyStats.risky++;
      } else {
        safetyStats.safe++;
      }
    });

    const result = {
      summary: {
        total_tools: tools.length,
        safety_filtered: safetyStats.safe + safetyStats.risky,
        forbidden_count: safetyStats.forbidden,
        safety_stats: safetyStats
      },
      tools: processedTools
    };

    console.log(`✅ Analysis complete: ${safetyStats.safe} safe, ${safetyStats.risky} risky, ${safetyStats.forbidden} forbidden`);
    return result;
  }

  /**
   * Analyze individual tool for AI agent use
   */
  _analyzeToolForAgent(tool, toolId) {
    return {
      id: toolId,
      name: tool.name || tool.operationId || 'unnamed_tool',
      method: (tool.method || 'GET').toUpperCase(),
      path: tool.pathTemplate || tool.path || '',
      operationId: tool.operationId || '',
      
      // Safety analysis
      safety_flags: this._analyzeSafety(tool),
      
      // Authentication requirements
      auth_requirements: this._analyzeAuthentication(tool),
      
      // Endpoint information for LLM
      endpoint_info: this._extractEndpointInfo(tool)
    };
  }

  /**
   * Safety analysis - detect danger patterns
   */
  _analyzeSafety(tool) {
    const dangerKeywords = [];
    const path = (tool.pathTemplate || tool.path || '').toLowerCase();
    const operationId = (tool.operationId || '').toLowerCase();
    const description = (tool.description || '').toLowerCase();
    const method = (tool.method || 'GET').toUpperCase();
    
    // Check all text fields for danger patterns
    const textToCheck = `${path} ${operationId} ${description}`;
    
    Object.entries(this.dangerPatterns).forEach(([category, patterns]) => {
      patterns.forEach(pattern => {
        if (pattern.test(textToCheck)) {
          dangerKeywords.push(category.toLowerCase().replace('_', '-'));
        }
      });
    });

    // Determine if forbidden
    const isForbidden = dangerKeywords.length > 0 || 
                       (method === 'DELETE' && this._hasHighImpactPatterns(textToCheck));

    return {
      is_forbidden: isForbidden,
      danger_keywords: [...new Set(dangerKeywords)], // Remove duplicates
      http_method_risk: this.methodRisk[method] || 'MEDIUM',
      requires_path_params: this._hasPathParams(tool)
    };
  }

  /**
   * Authentication analysis
   */
  _analyzeAuthentication(tool) {
    const authHeaders = [];
    const customAuthHeaders = [];
    let authDescription = '';
    
    // Check parameters for auth headers
    if (tool.parameters) {
      tool.parameters.forEach(param => {
        if (param.in === 'header') {
          const paramName = param.name.toLowerCase();
          
          if (paramName === 'authorization') {
            authHeaders.push('Authorization');
            authDescription = param.description || 'Bearer token required';
          } else if (paramName.includes('auth') || paramName.includes('key') || paramName.includes('token')) {
            customAuthHeaders.push(param.name);
            if (param.description) {
              authDescription = param.description;
            }
          }
        }
      });
    }

    // Check security requirements from OpenAPI
    const hasSecurityRequirements = tool.securityRequirements && tool.securityRequirements.length > 0;

    return {
      auth_required: authHeaders.length > 0 || customAuthHeaders.length > 0 || hasSecurityRequirements,
      auth_headers: authHeaders,
      custom_auth_headers: customAuthHeaders,
      auth_description: authDescription
    };
  }

  /**
   * Extract endpoint information for LLM analysis
   */
  _extractEndpointInfo(tool) {
    return {
      summary: tool.summary || tool.description || '',
      description: tool.description || '',
      tags: tool.tags || [],
      input_schema: this._extractInputSchema(tool),
      response_info: this._extractResponseInfo(tool)
    };
  }

  /**
   * Extract structured input schema
   */
  _extractInputSchema(tool) {
    const schema = {
      path_parameters: [],
      query_parameters: [],
      header_parameters: [],
      request_body: null
    };

    // Process parameters
    if (tool.parameters) {
      tool.parameters.forEach(param => {
        const paramInfo = {
          name: param.name,
          type: param.schema?.type || 'string',
          required: param.required || false,
          description: param.description || ''
        };

        switch (param.in) {
          case 'path':
            schema.path_parameters.push(paramInfo);
            break;
          case 'query':
            schema.query_parameters.push(paramInfo);
            break;
          case 'header':
            schema.header_parameters.push(paramInfo);
            break;
        }
      });
    }

    // Process request body
    if (tool.requestBody) {
      schema.request_body = {
        required: tool.requestBody.required || false,
        content_type: tool.requestBodyContentType || 'application/json',
        description: tool.requestBody.description || '',
        schema_ref: this._extractSchemaRef(tool.requestBody)
      };
    }

    return schema;
  }

  /**
   * Extract response information
   */
  _extractResponseInfo(tool) {
    // This is basic - could be enhanced if needed
    return {
      schema_ref: '', // Could extract from responses
      description: tool.summary || 'API response'
    };
  }

  /**
   * Helper: Check for high impact patterns
   */
  _hasHighImpactPatterns(text) {
    const highImpactPatterns = [
      /all/i, /many/i, /bulk/i, /batch/i,
      /organization/i, /org/i, /team/i,
      /admin/i, /system/i
    ];
    
    return highImpactPatterns.some(pattern => pattern.test(text));
  }

  /**
   * Helper: Check if tool has path parameters
   */
  _hasPathParams(tool) {
    if (tool.parameters) {
      return tool.parameters.some(param => param.in === 'path');
    }
    return (tool.pathTemplate || tool.path || '').includes('{');
  }

  /**
   * Helper: Extract schema reference from request body
   */
  _extractSchemaRef(requestBody) {
    try {
      if (requestBody.content) {
        const jsonContent = requestBody.content['application/json'];
        if (jsonContent?.schema?.$ref) {
          return jsonContent.schema.$ref;
        }
      }
    } catch (error) {
      // Ignore parsing errors
    }
    return '';
  }
} 