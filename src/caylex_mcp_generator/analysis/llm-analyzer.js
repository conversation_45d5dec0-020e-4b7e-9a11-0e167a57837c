/**
 * Enhanced LLM-Based Tool Analyzer for AI Agent Relevance
 * 
 * Two-phase analysis:
 * 1. Individual tool analysis (adaptive) - semantic relevance assessment
 * 2. Macro analysis (sequential) - workflow grouping and template creation
 */

import Anthropic from "@anthropic-ai/sdk";

/**
 * Sliding Window Processor - implements correct adaptive batch sizing
 * Based on actual success/failure counts, not hidden retries
 */
class SlidingWindowProcessor {
  constructor(options = {}) {
    this.initialBatchSize = options.initialBatchSize || 25;
    this.maxBatchSize = options.maxBatchSize || 35;
    this.minBatchSize = options.minBatchSize || 5;
    this.currentBatchSize = this.initialBatchSize;
    
    // Queue management
    this.allTools = [];           // Original tool list
    this.queuePosition = 0;       // Current position in queue
    this.failedTools = [];        // Tools that failed (rate limited)
    
    // Statistics
    this.batchAttempts = 0;
    this.totalProcessed = 0;
    this.totalFailed = 0;
    this.sizeHistory = [];        // Track batch size evolution
  }

  async processAllTools(tools, analyzer) {
    this.allTools = tools;
    this.queuePosition = 0;
    this.failedTools = [];
    const results = [];
    
    console.log(`⚡ Starting sliding window processing: ${tools.length} tools`);
    console.log(`📊 Initial batch size: ${this.currentBatchSize}`);
    
    while (this._hasMoreToProcess()) {
      const batch = this._createNextBatch();
      console.log(`🚀 Batch ${this.batchAttempts + 1}: ${batch.length} tools (size: ${this.currentBatchSize})`);
      console.log(`   Queue position: ${this.queuePosition}/${this.allTools.length}, Failed queue: ${this.failedTools.length}`);
      
      const batchResult = await this._processBatch(batch, analyzer);
      
      // Process results and update state
      results.push(...batchResult.successfulResults);
      this._updateStateAfterBatch(batchResult);
      
      this.batchAttempts++;
    }
    
    this._logFinalStats();
    return results;
  }

  _createNextBatch() {
    const batch = [];
    
    // Step 1: Add failed tools first (priority)
    batch.push(...this.failedTools);
    const failedCount = this.failedTools.length;
    this.failedTools = []; // Clear failed tools list
    
    // Step 2: Fill remaining spots with next tools from queue
    const remainingSpots = Math.max(0, this.currentBatchSize - batch.length);
    const availableTools = Math.max(0, this.allTools.length - this.queuePosition);
    const toolsToTake = Math.min(remainingSpots, availableTools);
    
    for (let i = 0; i < toolsToTake; i++) {
      batch.push({
        ...this.allTools[this.queuePosition + i],
        toolIndex: this.queuePosition + i
      });
    }
    
    // Adjust batch size if failed tools exceed current batch size
    const actualBatchSize = batch.length;
    if (actualBatchSize > this.currentBatchSize) {
      console.log(`   📈 Expanding batch to ${actualBatchSize} to accommodate ${failedCount} retries`);
      this.currentBatchSize = Math.min(actualBatchSize, this.maxBatchSize);
    }
    
    if (failedCount > 0) {
      console.log(`   📋 Batch composition: ${failedCount} retries + ${toolsToTake} new tools`);
    }
    
    return batch;
  }

  async _processBatch(batch, analyzer) {
    const startTime = Date.now();
    
    // Process all tools in parallel (NO INDIVIDUAL RETRIES!)
    const promises = batch.map(tool => 
      analyzer._analyzeIndividualToolOnce(tool, `tool_${tool.toolIndex}`)
    );
    
    const results = await Promise.allSettled(promises);
    
    const successfulResults = [];
    const newFailedTools = [];
    let rateLimitCount = 0;
    let otherErrors = 0;
    
    results.forEach((result, index) => {
      const tool = batch[index];
      
      if (result.status === 'fulfilled' && result.value.success) {
        successfulResults.push(result.value.result);
      } else {
        // Tool failed
        const errorInfo = result.status === 'fulfilled' ? result.value : { error: result.reason.message };
        
        if (errorInfo.isRateLimit || (errorInfo.error && errorInfo.error.includes('rate_limit'))) {
          rateLimitCount++;
          newFailedTools.push(tool); // Will be retried in next batch
        } else {
          // Non-rate-limit error - add default result and count
          otherErrors++;
          successfulResults.push({
            tool_id: `tool_${tool.toolIndex}`,
            error: errorInfo.error,
            ...analyzer._createDefaultAnalysis(`tool_${tool.toolIndex}`)
          });
        }
      }
    });
    
    return {
      successfulResults,
      newFailedTools,
      rateLimitCount,
      otherErrors,
      duration: Date.now() - startTime,
      totalProcessed: batch.length
    };
  }

  _updateStateAfterBatch(batchResult) {
    const { successfulResults, newFailedTools, rateLimitCount, otherErrors } = batchResult;
    
    // Update statistics
    this.totalProcessed += successfulResults.length;
    this.totalFailed += newFailedTools.length;
    
    // KEY LOGIC: Set next batch size to actual success count
    const oldBatchSize = this.currentBatchSize;
    const successCount = successfulResults.length;
    
    // New batch size = how many actually succeeded (with bounds)
    this.currentBatchSize = Math.max(successCount, this.minBatchSize);
    this.currentBatchSize = Math.min(this.currentBatchSize, this.maxBatchSize);
    
    // Track size history
    this.sizeHistory.push({
      attempt: this.batchAttempts + 1,
      oldSize: oldBatchSize,
      newSize: this.currentBatchSize,
      successful: successCount,
      rateLimited: newFailedTools.length,
      otherErrors
    });
    
    // Update queue position (advance by NEW successful tools only, not retries)
    const retryToolIndices = new Set(this.failedTools.map(f => f.toolIndex));
    const newToolsProcessed = successfulResults.filter(r => {
      const toolIndex = parseInt(r.tool_id.replace('tool_', ''));
      return !retryToolIndices.has(toolIndex);
    }).length;
    this.queuePosition += newToolsProcessed;
    
    // Store failed tools for next batch
    this.failedTools = newFailedTools;
    
    // Logging
    if (rateLimitCount > 0) {
      console.log(`⚠️ Rate limits: ${newFailedTools.length} tools failed, ${successCount} succeeded`);
      console.log(`📉 Adjusting batch size: ${oldBatchSize} → ${this.currentBatchSize} (based on success count)`);
    } else if (otherErrors > 0) {
      console.log(`⚠️ Other errors: ${otherErrors} tools had non-rate-limit errors`);
    }
    
    if (rateLimitCount === 0 && this.currentBatchSize !== oldBatchSize) {
      console.log(`📊 Batch size: ${oldBatchSize} → ${this.currentBatchSize} (${successCount} successful)`);
    }
  }

  _hasMoreToProcess() {
    return this.queuePosition < this.allTools.length || this.failedTools.length > 0;
  }

  _logFinalStats() {
    console.log(`\n🎯 SLIDING WINDOW STATS:`);
    console.log(`========================================`);
    console.log(`📊 Total batches: ${this.batchAttempts}`);
    console.log(`✅ Tools processed: ${this.totalProcessed}`);
    console.log(`⚠️ Rate limit failures: ${this.totalFailed}`);
    console.log(`🎪 Final batch size: ${this.currentBatchSize}`);
    console.log(`📈 Size evolution:`);
    
    this.sizeHistory.forEach(entry => {
      const direction = entry.newSize > entry.oldSize ? '📈' : 
                       entry.newSize < entry.oldSize ? '📉' : '➡️';
      console.log(`   Batch ${entry.attempt}: ${entry.oldSize} → ${entry.newSize} ${direction} (${entry.successful} ok, ${entry.rateLimited} rate limited, ${entry.otherErrors} other errors)`);
    });
    
    if (this.sizeHistory.length >= 3) {
      const lastThree = this.sizeHistory.slice(-3);
      const stable = lastThree.every(entry => entry.newSize === lastThree[0].newSize && entry.rateLimited === 0);
      if (stable) {
        console.log(`🎯 Converged to optimal batch size: ${this.currentBatchSize}`);
      }
    }
    
    console.log(`========================================\n`);
  }
}

export class LLMAnalyzer {
  constructor(options = {}) {
    this.anthropic = new Anthropic({
      apiKey: options.apiKey || process.env.ANTHROPIC_API_KEY
    });
    this.model = options.model || "claude-sonnet-4-20250514";
    this.maxRetries = options.maxRetries || 3;
    this.timeoutMs = options.timeoutMs || 30000;
    
    // Performance tracking
    this.stats = {
      totalTokensUsed: 0,
      totalApiCalls: 0,
      totalRetries: 0,
      totalTimeouts: 0,
      analysisStartTime: null,
      phaseTimings: {},
      averageTokensPerTool: 0
    };
  }
  
  /**
   * Main enhancement function - replaces the existing enhanceAnalysis
   */
  async enhanceAnalysis(patternAnalysis, context = {}) {
    this.stats.analysisStartTime = Date.now();
    const toolCount = patternAnalysis.tools.length;
    
    console.log(`🤖 Starting focused AI analysis for ${toolCount} tools...`);
    console.log(`📊 Analysis context: ${JSON.stringify(context)}`);
    console.log(`⚙️  Configuration: model=${this.model}, maxRetries=${this.maxRetries}, timeout=${this.timeoutMs}ms`);
    
    try {
      // Phase 1: Individual tool analysis (parallel, unlimited)
      console.log('🔍 Phase 1: Analyzing individual tools...');
      const phase1Start = Date.now();
      const individualAnalyses = await this.analyzeIndividualTools(patternAnalysis.tools);
      this.stats.phaseTimings.individualAnalysis = Date.now() - phase1Start;
      console.log(`✅ Phase 1 complete in ${this.stats.phaseTimings.individualAnalysis}ms`);
      
      // Phase 2: Macro analysis (sequential)
      console.log('📊 Phase 2: Running macro analysis...');
      const phase2Start = Date.now();
      const macroAnalysis = await this.analyzeMacroPatterns(individualAnalyses);
      this.stats.phaseTimings.macroAnalysis = Date.now() - phase2Start;
      console.log(`✅ Phase 2 complete in ${this.stats.phaseTimings.macroAnalysis}ms`);
      
      // Phase 3: Post-processing with computed metrics
      console.log('🧮 Phase 3: Computing metrics...');
      const phase3Start = Date.now();
      const enhancedMacroAnalysis = this._enhanceWithMetrics(macroAnalysis, individualAnalyses);
      this.stats.phaseTimings.metricsComputation = Date.now() - phase3Start;
      console.log(`✅ Phase 3 complete in ${this.stats.phaseTimings.metricsComputation}ms`);
      
      // Phase 4: Merge all results
      console.log('🔗 Phase 4: Merging results...');
      const phase4Start = Date.now();
      const finalResult = this._mergeAllAnalyses(
        patternAnalysis, 
        individualAnalyses, 
        enhancedMacroAnalysis
      );
      this.stats.phaseTimings.resultMerging = Date.now() - phase4Start;
      console.log(`✅ Phase 4 complete in ${this.stats.phaseTimings.resultMerging}ms`);
      
      const totalTime = Date.now() - this.stats.analysisStartTime;
      this._logFinalStats(toolCount, individualAnalyses.length, totalTime);
      
      return finalResult;
      
    } catch (error) {
      const totalTime = Date.now() - this.stats.analysisStartTime;
      console.error('🚨 LLM analysis failed:', error.message);
      console.error('📊 Failure stats:', {
        totalTime: `${totalTime}ms`,
        apiCalls: this.stats.totalApiCalls,
        retries: this.stats.totalRetries,
        timeouts: this.stats.totalTimeouts,
        tokensUsed: this.stats.totalTokensUsed
      });
      return this._createFallbackResult(patternAnalysis, error);
    }
  }
  
  /**
   * Phase 1: Analyze tools with sliding window processing - no individual retries!
   */
  async analyzeIndividualTools(tools) {
    const startTime = Date.now();
    
    console.log(`🔍 Analyzing ${tools.length} tools with sliding window processing...`);
    console.log(`⚡ Processing as fast as API limits allow`);
    
    // Create sliding window processor with smart initial configuration
    const processor = new SlidingWindowProcessor({
      initialBatchSize: tools.length > 100 ? 25 : 15,
      maxBatchSize: 35,
      minBatchSize: 5
    });
    
    try {
      // Use sliding window processing - no more individual retries!
      const individualAnalyses = await processor.processAllTools(tools, this);
      
      const duration = Date.now() - startTime;
      const successCount = individualAnalyses.filter(r => !r.error).length;
      const successRate = (successCount / tools.length * 100).toFixed(1);
      const toolsPerMinute = (tools.length / (duration / 60000)).toFixed(1);
      
      console.log(`🎉 Sliding window analysis complete in ${(duration/60000).toFixed(1)} minutes:`);
      console.log(`   📈 Success: ${successCount}/${tools.length} (${successRate}%)`);
      console.log(`   ⚡ Speed: ${toolsPerMinute} tools/minute`);
      console.log(`   🔄 Retries: ${this.stats.totalRetries}`);
      console.log(`   💰 Tokens used: ${this.stats.totalTokensUsed}`);
      if (this.stats.averageTokensPerTool > 0) {
        console.log(`   📊 Avg tokens/tool: ${Math.round(this.stats.averageTokensPerTool)}`);
      }
      
      return individualAnalyses;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`🚨 Sliding window analysis failed after ${(duration/60000).toFixed(1)} minutes:`, error.message);
      throw error;
    }
  }
  
  /**
   * Enhanced macro analysis with better JSON parsing
   */
  async analyzeMacroPatterns(individualAnalyses) {
    const startTime = Date.now();
    console.log('📊 Running macro analysis...');
    
    // Prepare data for macro analysis
    const macroData = this._prepareMacroData(individualAnalyses);
    console.log(`📋 Macro data prepared:`);
    console.log(`   🎯 High relevance tools (80+): ${macroData.highRelevanceCount}/${macroData.totalCount}`);
    console.log(`   🏷️  Workflow tags found: ${Object.keys(macroData.workflowTagCounts).length}`);
    console.log(`   📊 Functionality types: ${Object.keys(macroData.functionalityTypeCounts).join(', ')}`);
    
    // Build macro analysis prompt
    const prompt = this._buildMacroAnalysisPrompt(macroData);
    console.log(`📝 Macro prompt length: ${prompt.length} characters`);
    
    try {
      // ✅ Remove rate limit wait - sliding window already handles this
      // await this._waitForRateLimit();
      
      this.stats.totalApiCalls++;
      const apiStartTime = Date.now();
    
    const response = await this.anthropic.messages.create({
      model: this.model,
        max_tokens: 4000,
      temperature: 0.2,
        system: this._getMacroAnalysisSystemPrompt(),
        messages: [{ role: "user", content: prompt }]
      });
      
      const apiDuration = Date.now() - apiStartTime;
      
      // Track token usage if available
      if (response.usage) {
        const tokensUsed = response.usage.input_tokens + response.usage.output_tokens;
        this.stats.totalTokensUsed += tokensUsed;
        this.stats.currentMinuteTokens += tokensUsed;
        console.log(`💰 Macro analysis tokens: ${response.usage.input_tokens} in + ${response.usage.output_tokens} out`);
      }
      
      // Enhanced JSON parsing for macro analysis
      const responseText = response.content[0].text;
      const macroAnalysis = this._extractAndParseJSON(responseText);
      
      // Validate the structure
      this._validateMacroAnalysis(macroAnalysis);
      
      const duration = Date.now() - startTime;
      console.log(`✅ Macro analysis complete in ${duration}ms (API: ${apiDuration}ms)`);
      console.log(`   🔗 Workflow groups: ${Object.keys(macroAnalysis.workflow_groups).length}`);
      console.log(`   📋 Selection templates: ${Object.keys(macroAnalysis.selection_templates).length}`);
      
      // Log sample workflow groups
      Object.entries(macroAnalysis.workflow_groups).slice(0, 3).forEach(([key, workflow]) => {
        console.log(`   📁 ${workflow.name}: ${workflow.tools.length} tools (${workflow.priority} priority)`);
      });
      
      return {
        ...macroAnalysis,
        analysis_metadata: {
          timestamp: new Date().toISOString(),
          tools_analyzed: individualAnalyses.length,
          high_relevance_count: macroData.highRelevanceCount,
          processing_time_ms: duration,
          api_time_ms: apiDuration
        }
      };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`🚨 Macro analysis failed after ${duration}ms:`, error.message);
      console.error('📋 Macro data summary:', {
        totalTools: macroData.totalCount,
        highRelevance: macroData.highRelevanceCount,
        workflowTags: Object.keys(macroData.workflowTagCounts).length
      });
      return this._createFallbackMacroAnalysis(individualAnalyses);
    }
  }
  
  /**
   * Prepare data for macro analysis
   */
  _prepareMacroData(individualAnalyses) {
    const sortedByRelevance = individualAnalyses
      .sort((a, b) => b.relevance_score - a.relevance_score);
    
    return {
      totalCount: individualAnalyses.length,
      highRelevanceCount: individualAnalyses.filter(a => a.relevance_score >= 80).length,
      coreCount: individualAnalyses.filter(a => a.functionality_type === 'core').length,
      supportingCount: individualAnalyses.filter(a => a.functionality_type === 'supporting').length,
      top25Tools: sortedByRelevance.slice(0, 25).map(t => ({
        name: t.tool_id,
        relevance: t.relevance_score,
        type: t.functionality_type,
        workflows: t.workflow_tags || []
      })),
      workflowTagCounts: this._calculateWorkflowTagCounts(individualAnalyses),
      functionalityTypeCounts: this._calculateFunctionalityTypeCounts(individualAnalyses)
    };
  }

  /**
   * Build macro analysis prompt
   */
  _buildMacroAnalysisPrompt(macroData) {
    return `Based on individual tool analyses, create workflow groupings and selection templates:

## ANALYZED TOOLS SUMMARY
Total Tools: ${macroData.totalCount}
High Relevance (80+): ${macroData.highRelevanceCount}
Core Functionality: ${macroData.coreCount}
Supporting Functionality: ${macroData.supportingCount}

## TOP 25 TOOLS BY RELEVANCE
${macroData.top25Tools.map(t => 
  `- ${t.name} (${t.relevance}) [${t.type}] - Workflows: ${t.workflows.join(', ')}`
).join('\n')}

## WORKFLOW TAG DISTRIBUTION
${Object.entries(macroData.workflowTagCounts).map(([tag, count]) => 
  `- ${tag}: ${count} tools`
).join('\n')}

## FUNCTIONALITY TYPE DISTRIBUTION
${Object.entries(macroData.functionalityTypeCounts).map(([type, count]) => 
  `- ${type}: ${count} tools`
).join('\n')}

## ANALYSIS TASKS

1. **IDENTIFY CORE WORKFLOWS**: Group related tools into logical workflows
2. **CREATE SELECTION TEMPLATES**: Recommend tool selections for different use cases (NO tool count limits)
3. **PRIORITIZE BY RELEVANCE**: Rank workflows by importance for AI agents

**IMPORTANT**: The JSON structure below shows the required format with EXAMPLE values. 
Do NOT use the example values literally - analyze the actual tools and provide real results.
Do NOT include any computed metrics like averages, counts, or scores - focus on semantic analysis only.

{
  "workflow_groups": {
    "<actual_workflow_name>": {
      "name": "<descriptive workflow name>",
      "description": "<what this workflow accomplishes>",
      "tools": ["<actual_tool_ids_from_the_analysis_above>"],
      "priority": "<high|medium|low based on AI agent importance>",
      "use_cases": ["<specific use cases for AI agents>"]
    }
  },
  "selection_templates": {
    "<template_name>": {
      "name": "<template name>",
      "description": "<what this template provides>",
      "selection_criteria": {
        "min_relevance_score": <number>,
        "required_functionality_types": ["<types>"],
        "required_workflows": ["<workflow_names>"]
      },
      "selected_tools": ["<actual_tool_ids_that_meet_criteria>"],
      "reasoning": "<why these specific tools were selected>"
    }
  },
  "recommendations": {
    "highest_value_tools": ["<tool_id_1>", "<tool_id_2>"],
    "workflow_priorities": ["<workflow_1>", "<workflow_2>"],
    "complexity_warnings": ["<warning_1>"],
    "safety_exclusions": ["<tool_id>"],
    "reasoning": "<overall recommendation reasoning>"
  }
}`;
  }
  
  /**
   * Get system prompt for macro analysis
   */
  _getMacroAnalysisSystemPrompt() {
    return `You are an expert API strategist. You analyze collections of API endpoints to identify core workflows and create practical tool selection templates for AI agents.

Your goal is to group related tools into logical workflows and recommend optimal tool selections for different use cases.

Respond with valid JSON only.`;
  }

  /**
   * Phase 3: Enhance macro analysis with computed metrics
   */
  _enhanceWithMetrics(macroAnalysis, individualAnalyses) {
    const startTime = Date.now();
    console.log('🧮 Computing metrics for workflows and templates...');
    
    // Enhance workflow groups with computed metrics
    let workflowCount = 0;
    Object.keys(macroAnalysis.workflow_groups).forEach(workflowKey => {
      const workflow = macroAnalysis.workflow_groups[workflowKey];
      const workflowToolAnalyses = individualAnalyses.filter(analysis => 
        workflow.tools.includes(analysis.tool_id)
      );
      
      workflow.computed_metrics = {
        tool_count: workflow.tools.length,
        average_relevance: this._calculateAverage(
          workflowToolAnalyses.map(a => a.relevance_score)
        ),
        complexity_distribution: this._getComplexityDistribution(workflowToolAnalyses),
        risk_distribution: this._getRiskDistribution(workflowToolAnalyses)
      };
      
      workflowCount++;
    });
    
    // Enhance selection templates with computed metrics
    let templateCount = 0;
    Object.keys(macroAnalysis.selection_templates).forEach(templateKey => {
      const template = macroAnalysis.selection_templates[templateKey];
      const templateToolAnalyses = individualAnalyses.filter(analysis => 
        template.selected_tools.includes(analysis.tool_id)
      );
      
      template.computed_quality = {
        tool_count: template.selected_tools.length,
        average_relevance: this._calculateAverage(
          templateToolAnalyses.map(a => a.relevance_score)
        ),
        workflow_coverage: this._calculateWorkflowCoverage(
          templateToolAnalyses, macroAnalysis.workflow_groups
        ),
        coverage_score: this._calculateCoverageScore(
          templateToolAnalyses, macroAnalysis.workflow_groups
        ),
        complexity_breakdown: this._getComplexityDistribution(templateToolAnalyses)
      };
      
      templateCount++;
    });
    
    const duration = Date.now() - startTime;
    console.log(`✅ Metrics computed in ${duration}ms:`);
    console.log(`   📊 Enhanced ${workflowCount} workflow groups`);
    console.log(`   📋 Enhanced ${templateCount} selection templates`);
    
    return macroAnalysis;
  }

  /**
   * Phase 4: Merge all analysis phases into final enhanced result
   */
  _mergeAllAnalyses(patternAnalysis, individualAnalyses, macroAnalysis) {
    const startTime = Date.now();
    console.log('🔗 Merging all analysis results...');
    
    // Create enhanced tools with all analysis data
    const enhancedTools = patternAnalysis.tools.map((tool, index) => {
      const individualAnalysis = individualAnalyses.find(a => a.tool_id === `tool_${index}`);
      const workflowGroup = this._findWorkflowGroup(individualAnalysis, macroAnalysis);
      const templateRecommendations = this._findTemplateRecommendations(individualAnalysis, macroAnalysis);
      
      return {
        // Original pattern analysis data
        ...tool,
        
        // Individual LLM analysis
        relevance_score: individualAnalysis?.relevance_score || 50,
        functionality_type: individualAnalysis?.functionality_type || 'supporting',
        ai_use_cases: individualAnalysis?.ai_use_cases || [],
        workflow_tags: individualAnalysis?.workflow_tags || [],
        complexity_level: individualAnalysis?.complexity_level || 'moderate',
        impact_scope: individualAnalysis?.impact_scope || 'self-only',
        risk_assessment: individualAnalysis?.risk_assessment || 'medium',
        requires_context: individualAnalysis?.requires_context || [],
        
        // Macro analysis connections
        workflow_group: workflowGroup,
        recommended_in_templates: templateRecommendations,
        priority_rank: this._calculatePriorityRank(individualAnalysis, macroAnalysis)
      };
    });
    
    // Calculate final statistics
    const relevanceStats = this._calculateRelevanceStats(enhancedTools);
    const workflowStats = this._calculateWorkflowStats(macroAnalysis);
    
    // DETAILED LOGGING FOR DEBUGGING
    console.log(`\n🎯 DETAILED ANALYSIS RESULTS:`);
    console.log(`========================================`);
    
    // Log high relevance tools
    const highRelevanceTools = enhancedTools.filter(t => t.relevance_score >= 80);
    console.log(`🌟 HIGH RELEVANCE TOOLS (80+): ${highRelevanceTools.length}`);
    highRelevanceTools.forEach(tool => {
      console.log(`   • ${tool.name} (${tool.relevance_score}) - ${tool.functionality_type} - ${tool.method} ${tool.pathTemplate}`);
    });
    
    // Log workflow groups with their tools
    console.log(`\n📊 WORKFLOW GROUPS: ${Object.keys(macroAnalysis.workflow_groups).length}`);
    Object.entries(macroAnalysis.workflow_groups).forEach(([key, workflow]) => {
      console.log(`   📁 ${workflow.name} (${workflow.priority} priority):`);
      console.log(`      Tools: ${workflow.tools.length}`);
      workflow.tools.slice(0, 5).forEach(toolId => {
        const tool = enhancedTools.find(t => `tool_${enhancedTools.indexOf(t)}` === toolId);
        if (tool) {
          console.log(`      • ${tool.name} (${tool.relevance_score})`);
        }
      });
      if (workflow.tools.length > 5) {
        console.log(`      ... and ${workflow.tools.length - 5} more`);
      }
    });
    
    // Log selection templates with their tools
    console.log(`\n📋 SELECTION TEMPLATES: ${Object.keys(macroAnalysis.selection_templates).length}`);
    Object.entries(macroAnalysis.selection_templates).forEach(([key, template]) => {
      console.log(`   📝 ${template.name}:`);
      console.log(`      Description: ${template.description}`);
      console.log(`      Tools: ${template.selected_tools.length}`);
      console.log(`      Min relevance: ${template.selection_criteria.min_relevance_score}`);
      template.selected_tools.slice(0, 5).forEach(toolId => {
        const tool = enhancedTools.find(t => `tool_${enhancedTools.indexOf(t)}` === toolId);
        if (tool) {
          console.log(`      • ${tool.name} (${tool.relevance_score})`);
        }
      });
      if (template.selected_tools.length > 5) {
        console.log(`      ... and ${template.selected_tools.length - 5} more`);
      }
    });
    
    console.log(`========================================\n`);
    
    const duration = Date.now() - startTime;
    console.log(`✅ Results merged in ${duration}ms:`);
    console.log(`   🎯 Tool relevance distribution:`, relevanceStats);
    console.log(`   📊 Workflow distribution:`, workflowStats);
    
    return {
      // Original pattern analysis
      summary: patternAnalysis.summary,
      
      // Enhanced tools
      tools: enhancedTools,
      
      // Macro analysis results
      workflow_groups: macroAnalysis.workflow_groups,
      selection_templates: macroAnalysis.selection_templates,
      recommendations: macroAnalysis.recommendations,
      
      // Metadata
      analysis_metadata: {
        pattern_analysis_timestamp: new Date().toISOString(),
        individual_analysis_count: individualAnalyses.length,
        macro_analysis_timestamp: macroAnalysis.analysis_metadata?.timestamp,
        total_processing_time: Date.now() - this.stats.analysisStartTime,
        phase_timings: this.stats.phaseTimings,
        api_stats: {
          total_calls: this.stats.totalApiCalls,
          total_retries: this.stats.totalRetries,
          total_timeouts: this.stats.totalTimeouts,
          total_tokens: this.stats.totalTokensUsed
        }
      }
    };
  }

  // === HELPER METHODS ===

  /**
   * Log final comprehensive stats
   */
  _logFinalStats(originalToolCount, processedToolCount, totalTime) {
    console.log(`\n🎉 LLM Analysis Complete!`);
    console.log(`========================================`);
    console.log(`⏱️  Total time: ${totalTime}ms (${(totalTime/1000).toFixed(1)}s)`);
    console.log(`🎯 Tools processed: ${processedToolCount}/${originalToolCount}`);
    console.log(`📞 API calls made: ${this.stats.totalApiCalls}`);
    console.log(`🔄 Total retries: ${this.stats.totalRetries}`);
    console.log(`⏰ Timeouts: ${this.stats.totalTimeouts}`);
    console.log(`💰 Tokens used: ${this.stats.totalTokensUsed}`);
    console.log(`\n⚡ Phase breakdown:`);
    Object.entries(this.stats.phaseTimings).forEach(([phase, time]) => {
      const percentage = ((time / totalTime) * 100).toFixed(1);
      console.log(`   ${phase}: ${time}ms (${percentage}%)`);
    });
    
    if (this.stats.totalTokensUsed > 0) {
      const avgTokensPerTool = Math.round(this.stats.totalTokensUsed / processedToolCount);
      console.log(`\n💡 Efficiency metrics:`);
      console.log(`   Avg tokens/tool: ${avgTokensPerTool}`);
      console.log(`   Tools/second: ${(processedToolCount / (totalTime/1000)).toFixed(2)}`);
    }
    console.log(`========================================\n`);
  }

  /**
   * Calculate relevance statistics
   */
  _calculateRelevanceStats(tools) {
    const ranges = { 'high (80+)': 0, 'medium (50-79)': 0, 'low (<50)': 0 };
    tools.forEach(tool => {
      const score = tool.relevance_score || 0;
      if (score >= 80) ranges['high (80+)']++;
      else if (score >= 50) ranges['medium (50-79)']++;
      else ranges['low (<50)']++;
    });
    return ranges;
  }

  /**
   * Calculate workflow statistics
   */
  _calculateWorkflowStats(macroAnalysis) {
    const priorities = { high: 0, medium: 0, low: 0 };
    Object.values(macroAnalysis.workflow_groups || {}).forEach(workflow => {
      priorities[workflow.priority] = (priorities[workflow.priority] || 0) + 1;
    });
    return priorities;
  }

  /**
   * Create default analysis for failed tools
   */
  _createDefaultAnalysis(toolId) {
    return {
      relevance_score: 50,
      functionality_type: "supporting",
      ai_use_cases: ["General API functionality"],
      workflow_tags: ["uncategorized"],
      complexity_level: "moderate",
      impact_scope: "self-only",
      risk_assessment: "medium",
      requires_context: [],
      reasoning: "Default analysis due to processing failure"
    };
  }

  /**
   * Create fallback result when LLM analysis fails
   */
  _createFallbackResult(patternAnalysis, error) {
    console.warn('🔄 Creating fallback analysis result');
    
    return {
      ...patternAnalysis,
      tools: patternAnalysis.tools.map(tool => ({
        ...tool,
        relevance_score: 60, // Moderate default
        functionality_type: 'supporting',
        ai_use_cases: ['General API functionality'],
        workflow_tags: ['uncategorized'],
        complexity_level: 'moderate',
        impact_scope: 'self-only',
        risk_assessment: 'medium'
      })),
      workflow_groups: {},
      selection_templates: {},
      recommendations: {},
      error: {
        message: error.message,
        fallback_used: true,
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * Create fallback macro analysis if LLM fails
   */
  _createFallbackMacroAnalysis(individualAnalyses) {
    // Create basic groupings based on functionality types
    const coreTools = individualAnalyses
      .filter(a => a.functionality_type === 'core')
      .map(a => a.tool_id);
      
    return {
      workflow_groups: {
        "core-functionality": {
          name: "Core Functionality",
          description: "Essential API operations",
          tools: coreTools,
          priority: "high",
          use_cases: ["Basic API operations"]
        }
      },
      selection_templates: {
        "essentials": {
          name: "Essential Tools",
          description: "Basic functionality only",
          selection_criteria: {
            min_relevance_score: 70,
            required_functionality_types: ["core"]
          },
          selected_tools: coreTools,
          reasoning: "Fallback selection of core tools"
        }
      },
      recommendations: {
        highest_value_tools: coreTools.slice(0, 10),
        workflow_priorities: ["core-functionality"],
        complexity_warnings: [],
        safety_exclusions: []
      }
    };
  }

  /**
   * Validate macro analysis structure
   */
  _validateMacroAnalysis(analysis) {
    const required = ['workflow_groups', 'selection_templates', 'recommendations'];
    required.forEach(field => {
      if (!analysis[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    });
  }

  /**
   * Calculate workflow tag counts
   */
  _calculateWorkflowTagCounts(analyses) {
    const counts = {};
    analyses.forEach(analysis => {
      (analysis.workflow_tags || []).forEach(tag => {
        counts[tag] = (counts[tag] || 0) + 1;
      });
    });
    return counts;
  }

  /**
   * Calculate functionality type counts
   */
  _calculateFunctionalityTypeCounts(analyses) {
    const counts = {};
    analyses.forEach(analysis => {
      const type = analysis.functionality_type || 'unknown';
      counts[type] = (counts[type] || 0) + 1;
    });
    return counts;
  }

  /**
   * Calculate average of numbers
   */
  _calculateAverage(numbers) {
    if (numbers.length === 0) return 0;
    return Math.round(numbers.reduce((a, b) => a + b, 0) / numbers.length);
  }

  /**
   * Calculate coverage score
   */
  _calculateCoverageScore(toolAnalyses, workflowGroups) {
    const highPriorityWorkflows = Object.keys(workflowGroups)
      .filter(key => workflowGroups[key].priority === 'high');
    
    if (highPriorityWorkflows.length === 0) return 100;
    
    const coveredWorkflows = new Set();
    toolAnalyses.forEach(analysis => {
      (analysis.workflow_tags || []).forEach(tag => {
        if (highPriorityWorkflows.includes(tag)) {
          coveredWorkflows.add(tag);
        }
      });
    });
    
    return Math.round((coveredWorkflows.size / highPriorityWorkflows.length) * 100);
  }

  /**
   * Calculate workflow coverage
   */
  _calculateWorkflowCoverage(toolAnalyses, workflowGroups) {
    const coveredWorkflows = new Set();
    toolAnalyses.forEach(analysis => {
      (analysis.workflow_tags || []).forEach(tag => {
        coveredWorkflows.add(tag);
      });
    });
    return Array.from(coveredWorkflows);
  }

  /**
   * Get complexity distribution
   */
  _getComplexityDistribution(analyses) {
    const distribution = { simple: 0, moderate: 0, complex: 0 };
    analyses.forEach(analysis => {
      const complexity = analysis.complexity_level || 'moderate';
      distribution[complexity] = (distribution[complexity] || 0) + 1;
    });
    return distribution;
  }

  /**
   * Get risk distribution
   */
  _getRiskDistribution(analyses) {
    const distribution = { low: 0, medium: 0, high: 0 };
    analyses.forEach(analysis => {
      const risk = analysis.risk_assessment || 'medium';
      distribution[risk] = (distribution[risk] || 0) + 1;
    });
    return distribution;
  }

  /**
   * Find workflow group for a tool
   */
  _findWorkflowGroup(individualAnalysis, macroAnalysis) {
    if (!individualAnalysis || !macroAnalysis.workflow_groups) return null;
    
    for (const [workflowKey, workflow] of Object.entries(macroAnalysis.workflow_groups)) {
      if (workflow.tools.includes(individualAnalysis.tool_id)) {
        return workflowKey;
      }
    }
    return null;
  }

  /**
   * Find template recommendations for a tool
   */
  _findTemplateRecommendations(individualAnalysis, macroAnalysis) {
    if (!individualAnalysis || !macroAnalysis.selection_templates) return [];
    
    const recommendations = [];
    for (const [templateKey, template] of Object.entries(macroAnalysis.selection_templates)) {
      if (template.selected_tools.includes(individualAnalysis.tool_id)) {
        recommendations.push(templateKey);
      }
    }
    return recommendations;
  }

  /**
   * Calculate priority rank for a tool
   */
  _calculatePriorityRank(individualAnalysis, macroAnalysis) {
    if (!individualAnalysis) return 999;
    return 100 - (individualAnalysis.relevance_score || 50); // Lower rank = higher priority
  }

  /**
   * Enhanced JSON extraction that handles markdown code blocks
   */
  _extractAndParseJSON(text) {
    try {
      // First, try to extract JSON from markdown code blocks
      const jsonContent = this._extractJsonFromMarkdown(text);
      return JSON.parse(jsonContent);
    } catch (error) {
      // If that fails, try parsing the text directly
      try {
        return JSON.parse(text.trim());
      } catch (directParseError) {
        // If both fail, try to find JSON-like content in the text
        const jsonMatch = text.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          try {
            return JSON.parse(jsonMatch[0]);
          } catch (matchParseError) {
            throw new Error(`Failed to parse JSON: ${error.message}. Raw response: ${text.substring(0, 200)}...`);
          }
        }
        throw new Error(`No valid JSON found in response: ${text.substring(0, 200)}...`);
      }
    }
  }
  
  /**
   * Extract JSON content from markdown code blocks
   */
  _extractJsonFromMarkdown(text) {
    // Remove markdown code block formatting
    const jsonMatch = text.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
    if (jsonMatch) {
      return jsonMatch[1].trim();
    }
    
    // If no code blocks, return the text as-is (might already be JSON)
    return text.trim();
  }
  
  /**
   * Core individual tool analysis with token tracking
   */
  async _analyzeIndividualTool(tool, toolId) {
    const prompt = this._buildIndividualToolPrompt(tool);
    
    try {
      this.stats.totalApiCalls++;
      const startTime = Date.now();
      
      const response = await Promise.race([
        this.anthropic.messages.create({
          model: this.model,
          max_tokens: 800,
          temperature: 0.1,
          system: this._getIndividualToolSystemPrompt(),
          messages: [{ role: "user", content: prompt }]
        }),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), this.timeoutMs)
        )
      ]);
      
      const duration = Date.now() - startTime;
      let tokensUsed = 0;
      
      // Track token usage if available
      if (response.usage) {
        tokensUsed = response.usage.input_tokens + response.usage.output_tokens;
        this.stats.totalTokensUsed += tokensUsed;
        
        // Update average tokens per tool
        const currentAverage = this.stats.averageTokensPerTool;
        this.stats.averageTokensPerTool = currentAverage === 0 ? 
          tokensUsed : 
          (currentAverage * 0.8) + (tokensUsed * 0.2);
      }
      
      // Extract and parse JSON with proper markdown handling
      const responseText = response.content[0].text;
      const analysis = this._extractAndParseJSON(responseText);
      
      // Log detailed analysis for first few tools (for debugging)
      if (this.stats.totalApiCalls <= 3) {
        console.log(`🔍 Sample analysis for ${toolId} (${duration}ms, ${tokensUsed} tokens):`);
        console.log(`   Relevance: ${analysis.relevance_score}/100`);
        console.log(`   Type: ${analysis.functionality_type}`);
        console.log(`   Workflows: ${(analysis.workflow_tags || []).join(', ')}`);
      }
      
      return {
        tool_id: toolId,
        timestamp: new Date().toISOString(),
        processing_time_ms: duration,
        tokens_used: tokensUsed,
        ...analysis
      };
      
    } catch (error) {
      if (error.message === 'Timeout') {
        throw new Error('Timeout');
      }
      
      // Handle rate limit errors specifically
      if (error.message && error.message.includes('rate_limit_error')) {
        throw new Error(`Rate limit: ${error.message}`);
      }
      
      throw new Error(`API call failed: ${error.message}`);
    }
  }

  /**
   * Analyze single tool once - NO RETRIES (batch processor handles rate limits)
   */
  async _analyzeIndividualToolOnce(tool, toolId) {
    try {
      const result = await this._analyzeIndividualTool(tool, toolId);
      return { success: true, result };
    } catch (error) {
      return { 
        success: false, 
        error: error.message,
        isRateLimit: error.message.includes('rate_limit') || error.message.includes('Rate limit'),
        tool,
        toolId 
      };
    }
  }

  /**
   * Build individual tool analysis prompt
   */
  _buildIndividualToolPrompt(tool) {
    const endpointInfo = tool.endpoint_info || {};
    const safetyFlags = tool.safety_flags || {};
    const authRequirements = tool.auth_requirements || {};

    return `Analyze this API endpoint for AI agent relevance:

## ENDPOINT DETAILS
- **Name**: ${tool.name || 'unnamed_tool'}
- **Method**: ${tool.method} ${tool.path}
- **Summary**: ${endpointInfo.summary || 'No summary'}
- **Description**: ${endpointInfo.description || 'No description'}
- **Tags**: ${(endpointInfo.tags || []).join(', ') || 'None'}

## INPUT REQUIREMENTS
- **Path Parameters**: ${this._formatParameters(endpointInfo.input_schema?.path_parameters)}
- **Query Parameters**: ${this._formatParameters(endpointInfo.input_schema?.query_parameters)}
- **Request Body**: ${endpointInfo.input_schema?.request_body ? 'Required' : 'None'}
- **Authentication**: ${authRequirements.auth_required ? authRequirements.auth_description || 'Required' : 'None'}

## SAFETY CONTEXT
- **Risk Level**: ${safetyFlags.http_method_risk || 'UNKNOWN'}
- **Danger Signals**: ${(safetyFlags.danger_keywords || []).join(', ') || 'None'}
- **Forbidden**: ${safetyFlags.is_forbidden ? 'YES' : 'NO'}

## CORE ANALYSIS QUESTIONS

1. **RELEVANCE (0-100)**: How useful is this for AI agents helping users?
   - 90-100: Essential core functionality (login, get profile, create content)
   - 70-89: Important supporting functionality (search, list items, update settings)
   - 50-69: Useful but non-essential (advanced filtering, bulk operations)
   - 30-49: Niche functionality (admin settings, edge cases)
   - 0-29: Irrelevant for typical AI agent use (system internals, debugging)

2. **FUNCTIONALITY TYPE**: 
   - "core": Essential user-facing functionality
   - "supporting": Helpful but not essential
   - "admin": Administrative/management functions
   - "edge-case": Handles rare scenarios
   - "system": Internal system functions

3. **AI USE CASES**: What would an AI agent actually do with this?

4. **WORKFLOW CONTEXT**: What user workflows would need this tool?

5. **COMPLEXITY ASSESSMENT**: How hard is this for an AI to use correctly?

6. **IMPACT SCOPE**: Who/what does this affect?
   - "self-only": Only affects the requesting user
   - "team": Affects user's team/workspace
   - "organization": Affects entire organization
   - "system": System-wide effects

**IMPORTANT**: The JSON structure below shows the required format with EXAMPLE values. 
Do NOT use the example values literally - provide actual analysis results.

{
  "relevance_score": <0-100_number>,
  "functionality_type": "<core|supporting|admin|edge-case|system>",
  "ai_use_cases": [
    "<specific use case 1>",
    "<specific use case 2>"
  ],
  "workflow_tags": [
    "<workflow_category_1>", 
    "<workflow_category_2>"
  ],
  "complexity_level": "<simple|moderate|complex>",
  "impact_scope": "<self-only|team|organization|system>",
  "risk_assessment": "<low|medium|high>",
  "requires_context": [
    "<context_requirement_1>"
  ],
  "reasoning": "<explanation of analysis>"
}`;
  }

  /**
   * Get system prompt for individual tool analysis
   */
  _getIndividualToolSystemPrompt() {
    return `You are an expert API analyst specializing in AI agent tool selection. Your job is to analyze individual API endpoints and determine their relevance for AI agents that help users accomplish tasks.

CORE CRITERIA FOR AI AGENT TOOLS:
1. RELEVANCE: Does this help AI agents get useful context or perform valuable actions?
2. CORE vs EDGE: Is this core functionality or a niche edge case?
3. SAFETY: Can AI agents use this safely without causing harm?
4. COMPLEXITY: Can AI agents use this correctly without deep domain expertise?

You must respond with valid JSON only, following the exact schema provided.`;
  }

  /**
   * Format parameters for display
   */
  _formatParameters(params) {
    if (!params || params.length === 0) return 'None';
    return params.map(p => `${p.name} (${p.type})`).join(', ');
  }

  /**
   * Utility delay function for retries
   */
  _delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
} 