"""
Enhanced Pattern-Based Tool Analyzer for AI Agent Relevance

Focuses on reliable detection of:
- Safety signals (danger patterns, HTTP method risk)
- Authentication requirements
- Input schema extraction
- Endpoint metadata for LLM analysis
"""

import re
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)


class PatternAnalyzer:
    """Pattern-based analyzer for detecting danger patterns and extracting tool metadata."""
    
    def __init__(self):
        """Initialize the pattern analyzer with danger patterns and risk levels."""
        # Danger patterns we can reliably detect
        self.danger_patterns = {
            "ADMIN_OPERATIONS": [
                re.compile(r"/admin(?:/|$)", re.IGNORECASE),
                re.compile(r"/manage(?:/|$)", re.IGNORECASE),
                re.compile(r"/system(?:/|$)", re.IGNORECASE),
                re.compile(r"/config(?:/|$)", re.IGNORECASE),
                re.compile(r"/settings(?:/|$)", re.IGNORECASE)
            ],
            
            "BULK_OPERATIONS": [
                re.compile(r"(?:delete|update|create|modify).*(?:all|many|bulk)", re.IGNORECASE),
                re.compile(r"(?:all|many|bulk).*(?:delete|update|create|modify)", re.IGNORECASE),
                re.compile(r"batch.*(?:delete|update|create|modify)", re.IGNORECASE),
                re.compile(r".*(?:all|many|bulk)$", re.IGNORECASE)
            ],
            
            "FINANCIAL_OPERATIONS": [
                re.compile(r"/payment(?:s)?(?:/|$)", re.IGNORECASE),
                re.compile(r"/billing(?:/|$)", re.IGNORECASE),
                re.compile(r"/treasury(?:/|$)", re.IGNORECASE),
                re.compile(r"/transaction(?:s)?(?:/|$)", re.IGNORECASE),
                re.compile(r"/charge(?:s)?(?:/|$)", re.IGNORECASE),
                re.compile(r"/refund(?:s)?(?:/|$)", re.IGNORECASE),
                re.compile(r"/invoice(?:s)?(?:/|$)", re.IGNORECASE),
                re.compile(r"/subscription(?:s)?(?:/|$)", re.IGNORECASE)
            ],
            
            "ORGANIZATION_OPERATIONS": [
                re.compile(r"/org(?:anization)?s?(?:/|$)", re.IGNORECASE),
                re.compile(r"/team(?:s)?(?:/|$)", re.IGNORECASE),
                re.compile(r"/workspace(?:s)?(?:/|$)", re.IGNORECASE),
                re.compile(r"/tenant(?:s)?(?:/|$)", re.IGNORECASE)
            ],
            
            "INTEGRATION_OPERATIONS": [
                re.compile(r"/webhook(?:s)?(?:/|$)", re.IGNORECASE),
                re.compile(r"/integration(?:s)?(?:/|$)", re.IGNORECASE),
                re.compile(r"/callback(?:s)?(?:/|$)", re.IGNORECASE),
                re.compile(r"/api[_-]?key(?:s)?(?:/|$)", re.IGNORECASE)
            ],
            
            "SYSTEM_OPERATIONS": [
                re.compile(r"/migration(?:s)?(?:/|$)", re.IGNORECASE),
                re.compile(r"/backup(?:s)?(?:/|$)", re.IGNORECASE),
                re.compile(r"/export(?:s)?(?:/|$)", re.IGNORECASE),
                re.compile(r"/import(?:s)?(?:/|$)", re.IGNORECASE),
                re.compile(r"/sync(?:/|$)", re.IGNORECASE),
                re.compile(r"/deploy(?:/|$)", re.IGNORECASE)
            ]
        }

        # HTTP method risk levels
        self.method_risk = {
            'GET': 'LOW',
            'POST': 'MEDIUM', 
            'PUT': 'MEDIUM',
            'PATCH': 'MEDIUM',
            'DELETE': 'HIGH'
        }
    
    def analyze(self, tools: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Main analysis function - simplified and focused
        
        Args:
            tools: List of tool definitions to analyze
            
        Returns:
            Analysis result with processed tools and safety statistics
        """
        logger.info(f"🔍 Analyzing {len(tools)} tools for AI agent relevance...")
        
        processed_tools = []
        safety_stats = {"safe": 0, "forbidden": 0, "risky": 0}
        
        for index, tool in enumerate(tools):
            analysis = self._analyze_tool_for_agent(tool, f"tool_{index}")
            processed_tools.append(analysis)
            
            # Update safety stats
            if analysis["safety_flags"]["is_forbidden"]:
                safety_stats["forbidden"] += 1
            elif analysis["safety_flags"]["danger_keywords"]:
                safety_stats["risky"] += 1
            else:
                safety_stats["safe"] += 1

        result = {
            "summary": {
                "total_tools": len(tools),
                "safety_filtered": safety_stats["safe"] + safety_stats["risky"],
                "forbidden_count": safety_stats["forbidden"],
                "safety_stats": safety_stats
            },
            "tools": processed_tools
        }

        logger.info(f"✅ Analysis complete: {safety_stats['safe']} safe, {safety_stats['risky']} risky, {safety_stats['forbidden']} forbidden")
        return result

    def _analyze_tool_for_agent(self, tool: Dict[str, Any], tool_id: str) -> Dict[str, Any]:
        """
        Analyze individual tool for AI agent use
        
        Args:
            tool: Tool definition to analyze
            tool_id: Unique identifier for the tool
            
        Returns:
            Analysis result for the individual tool
        """
        return {
            "id": tool_id,
            "name": tool.get("name") or tool.get("operationId") or "unnamed_tool",
            "method": (tool.get("method") or "GET").upper(),
            "path": tool.get("pathTemplate") or tool.get("path") or "",
            "operationId": tool.get("operationId") or "",
            
            # Safety analysis
            "safety_flags": self._analyze_safety(tool),
            
            # Authentication requirements
            "auth_requirements": self._analyze_authentication(tool),
            
            # Endpoint information for LLM
            "endpoint_info": self._extract_endpoint_info(tool)
        }

    def _analyze_safety(self, tool: Dict[str, Any]) -> Dict[str, Any]:
        """
        Safety analysis - detect danger patterns
        
        Args:
            tool: Tool definition to analyze
            
        Returns:
            Safety analysis results
        """
        danger_keywords = []
        path = (tool.get("pathTemplate") or tool.get("path") or "").lower()
        operation_id = (tool.get("operationId") or "").lower()
        description = (tool.get("description") or "").lower()
        method = (tool.get("method") or "GET").upper()
        
        # Check all text fields for danger patterns
        text_to_check = f"{path} {operation_id} {description}"
        
        for category, patterns in self.danger_patterns.items():
            for pattern in patterns:
                if pattern.search(text_to_check):
                    danger_keywords.append(category.lower().replace("_", "-"))

        # Determine if forbidden
        is_forbidden = (
            len(danger_keywords) > 0 or 
            (method == "DELETE" and self._has_high_impact_patterns(text_to_check))
        )

        return {
            "is_forbidden": is_forbidden,
            "danger_keywords": list(set(danger_keywords)),  # Remove duplicates
            "http_method_risk": self.method_risk.get(method, "MEDIUM"),
            "requires_path_params": self._has_path_params(tool)
        }

    def _analyze_authentication(self, tool: Dict[str, Any]) -> Dict[str, Any]:
        """
        Authentication analysis
        
        Args:
            tool: Tool definition to analyze
            
        Returns:
            Authentication requirements analysis
        """
        auth_headers = []
        custom_auth_headers = []
        auth_description = ""
        
        # Check parameters for auth headers
        parameters = tool.get("parameters", [])
        for param in parameters:
            if param.get("in") == "header":
                param_name = param.get("name", "").lower()
                
                if param_name == "authorization":
                    auth_headers.append("Authorization")
                    auth_description = param.get("description", "Bearer token required")
                elif any(keyword in param_name for keyword in ["auth", "key", "token"]):
                    custom_auth_headers.append(param.get("name", ""))
                    if param.get("description"):
                        auth_description = param["description"]

        # Check security requirements from OpenAPI
        has_security_requirements = bool(tool.get("securityRequirements"))

        return {
            "auth_required": bool(auth_headers or custom_auth_headers or has_security_requirements),
            "auth_headers": auth_headers,
            "custom_auth_headers": custom_auth_headers,
            "auth_description": auth_description
        }

    def _extract_endpoint_info(self, tool: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract endpoint information for LLM analysis
        
        Args:
            tool: Tool definition to analyze
            
        Returns:
            Endpoint information for LLM processing
        """
        return {
            "summary": tool.get("summary") or tool.get("description") or "",
            "description": tool.get("description") or "",
            "tags": tool.get("tags") or [],
            "input_schema": self._extract_input_schema(tool),
            "response_info": self._extract_response_info(tool)
        }

    def _extract_input_schema(self, tool: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract structured input schema
        
        Args:
            tool: Tool definition to analyze
            
        Returns:
            Structured input schema information
        """
        schema = {
            "path_parameters": [],
            "query_parameters": [],
            "header_parameters": [],
            "request_body": None
        }

        # Process parameters
        parameters = tool.get("parameters", [])
        for param in parameters:
            param_info = {
                "name": param.get("name", ""),
                "type": param.get("schema", {}).get("type", "string"),
                "required": param.get("required", False),
                "description": param.get("description", "")
            }

            param_in = param.get("in")
            if param_in == "path":
                schema["path_parameters"].append(param_info)
            elif param_in == "query":
                schema["query_parameters"].append(param_info)
            elif param_in == "header":
                schema["header_parameters"].append(param_info)

        # Process request body
        request_body = tool.get("requestBody")
        if request_body:
            schema["request_body"] = {
                "required": request_body.get("required", False),
                "content_type": tool.get("requestBodyContentType", "application/json"),
                "description": request_body.get("description", ""),
                "schema_ref": self._extract_schema_ref(request_body)
            }

        return schema

    def _extract_response_info(self, tool: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract response information
        
        Args:
            tool: Tool definition to analyze
            
        Returns:
            Response information
        """
        # This is basic - could be enhanced if needed
        return {
            "schema_ref": "",  # Could extract from responses
            "description": tool.get("summary", "API response")
        }

    def _has_high_impact_patterns(self, text: str) -> bool:
        """
        Helper: Check for high impact patterns
        
        Args:
            text: Text to check for patterns
            
        Returns:
            True if high impact patterns are found
        """
        high_impact_patterns = [
            re.compile(r"all", re.IGNORECASE),
            re.compile(r"many", re.IGNORECASE),
            re.compile(r"bulk", re.IGNORECASE),
            re.compile(r"batch", re.IGNORECASE),
            re.compile(r"organization", re.IGNORECASE),
            re.compile(r"org", re.IGNORECASE),
            re.compile(r"team", re.IGNORECASE),
            re.compile(r"admin", re.IGNORECASE),
            re.compile(r"system", re.IGNORECASE)
        ]
        
        return any(pattern.search(text) for pattern in high_impact_patterns)

    def _has_path_params(self, tool: Dict[str, Any]) -> bool:
        """
        Helper: Check if tool has path parameters
        
        Args:
            tool: Tool definition to check
            
        Returns:
            True if tool has path parameters
        """
        parameters = tool.get("parameters", [])
        if any(param.get("in") == "path" for param in parameters):
            return True
        
        path = tool.get("pathTemplate") or tool.get("path") or ""
        return "{" in path

    def _extract_schema_ref(self, request_body: Dict[str, Any]) -> str:
        """
        Helper: Extract schema reference from request body
        
        Args:
            request_body: Request body definition
            
        Returns:
            Schema reference string
        """
        try:
            content = request_body.get("content", {})
            json_content = content.get("application/json", {})
            schema = json_content.get("schema", {})
            return schema.get("$ref", "")
        except Exception:
            # Ignore parsing errors
            return ""
