"""
Enhanced LLM-Based Tool Analyzer for AI Agent Relevance

Two-phase analysis:
1. Individual tool analysis (adaptive) - semantic relevance assessment
2. Macro analysis (sequential) - workflow grouping and template creation
"""

import asyncio
import json
import time
from typing import Dict, List, Any, Optional, Set
import logging

try:
    from anthropic import Anthropic
except ImportError:
    Anthropic = None

logger = logging.getLogger(__name__)


class SlidingWindowProcessor:
    """
    Sliding Window Processor - implements correct adaptive batch sizing
    Based on actual success/failure counts, not hidden retries
    """
    
    def __init__(self, options: Optional[Dict[str, Any]] = None):
        """Initialize the sliding window processor with configuration options."""
        if options is None:
            options = {}
            
        self.initial_batch_size = options.get("initial_batch_size", 25)
        self.max_batch_size = options.get("max_batch_size", 35)
        self.min_batch_size = options.get("min_batch_size", 5)
        self.current_batch_size = self.initial_batch_size
        
        # Queue management
        self.all_tools = []           # Original tool list
        self.queue_position = 0       # Current position in queue
        self.failed_tools = []        # Tools that failed (rate limited)
        
        # Statistics
        self.batch_attempts = 0
        self.total_processed = 0
        self.total_failed = 0
        self.size_history = []        # Track batch size evolution

    async def process_all_tools(self, tools: List[Dict[str, Any]], analyzer: 'LLMAnalyzer') -> List[Dict[str, Any]]:
        """
        Process all tools using sliding window approach
        
        Args:
            tools: List of tools to process
            analyzer: LLMAnalyzer instance to use for processing
            
        Returns:
            List of analysis results
        """
        self.all_tools = tools
        self.queue_position = 0
        self.failed_tools = []
        results = []
        
        logger.info(f"⚡ Starting sliding window processing: {len(tools)} tools")
        logger.info(f"📊 Initial batch size: {self.current_batch_size}")
        
        while self._has_more_to_process():
            batch = self._create_next_batch()
            logger.info(f"🚀 Batch {self.batch_attempts + 1}: {len(batch)} tools (size: {self.current_batch_size})")
            logger.info(f"   Queue position: {self.queue_position}/{len(self.all_tools)}, Failed queue: {len(self.failed_tools)}")
            
            batch_result = await self._process_batch(batch, analyzer)
            
            # Process results and update state
            results.extend(batch_result["successful_results"])
            self._update_state_after_batch(batch_result)
            
            self.batch_attempts += 1
        
        self._log_final_stats()
        return results

    def _create_next_batch(self) -> List[Dict[str, Any]]:
        """Create the next batch of tools to process."""
        batch = []
        
        # Step 1: Add failed tools first (priority)
        batch.extend(self.failed_tools)
        failed_count = len(self.failed_tools)
        self.failed_tools = []  # Clear failed tools list
        
        # Step 2: Fill remaining spots with next tools from queue
        remaining_spots = max(0, self.current_batch_size - len(batch))
        available_tools = max(0, len(self.all_tools) - self.queue_position)
        tools_to_take = min(remaining_spots, available_tools)
        
        for i in range(tools_to_take):
            tool = self.all_tools[self.queue_position + i].copy()
            tool["tool_index"] = self.queue_position + i
            batch.append(tool)
        
        # Adjust batch size if failed tools exceed current batch size
        actual_batch_size = len(batch)
        if actual_batch_size > self.current_batch_size:
            logger.info(f"   📈 Expanding batch to {actual_batch_size} to accommodate {failed_count} retries")
            self.current_batch_size = min(actual_batch_size, self.max_batch_size)
        
        if failed_count > 0:
            logger.info(f"   📋 Batch composition: {failed_count} retries + {tools_to_take} new tools")
        
        return batch

    async def _process_batch(self, batch: List[Dict[str, Any]], analyzer: 'LLMAnalyzer') -> Dict[str, Any]:
        """Process a batch of tools in parallel."""
        start_time = time.time()
        
        # Process all tools in parallel (NO INDIVIDUAL RETRIES!)
        tasks = [
            analyzer._analyze_individual_tool_once(tool, f"tool_{tool['tool_index']}")
            for tool in batch
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        successful_results = []
        new_failed_tools = []
        rate_limit_count = 0
        other_errors = 0
        
        for i, result in enumerate(results):
            tool = batch[i]
            
            if isinstance(result, Exception):
                # Handle exception
                error_msg = str(result)
                if "rate_limit" in error_msg.lower():
                    rate_limit_count += 1
                    new_failed_tools.append(tool)  # Will be retried in next batch
                else:
                    # Non-rate-limit error - add default result and count
                    other_errors += 1
                    successful_results.append({
                        "tool_id": f"tool_{tool['tool_index']}",
                        "error": error_msg,
                        **analyzer._create_default_analysis(f"tool_{tool['tool_index']}")
                    })
            elif isinstance(result, dict) and result.get("success"):
                successful_results.append(result["result"])
            else:
                # Tool failed
                error_info = result if isinstance(result, dict) else {"error": str(result)}
                
                if error_info.get("is_rate_limit") or (error_info.get("error") and "rate_limit" in error_info["error"]):
                    rate_limit_count += 1
                    new_failed_tools.append(tool)  # Will be retried in next batch
                else:
                    # Non-rate-limit error - add default result and count
                    other_errors += 1
                    successful_results.append({
                        "tool_id": f"tool_{tool['tool_index']}",
                        "error": error_info.get("error", "Unknown error"),
                        **analyzer._create_default_analysis(f"tool_{tool['tool_index']}")
                    })
        
        return {
            "successful_results": successful_results,
            "new_failed_tools": new_failed_tools,
            "rate_limit_count": rate_limit_count,
            "other_errors": other_errors,
            "duration": (time.time() - start_time) * 1000,  # Convert to milliseconds
            "total_processed": len(batch)
        }

    def _update_state_after_batch(self, batch_result: Dict[str, Any]) -> None:
        """Update processor state after batch completion."""
        successful_results = batch_result["successful_results"]
        new_failed_tools = batch_result["new_failed_tools"]
        rate_limit_count = batch_result["rate_limit_count"]
        other_errors = batch_result["other_errors"]
        
        # Update statistics
        self.total_processed += len(successful_results)
        self.total_failed += len(new_failed_tools)
        
        # KEY LOGIC: Set next batch size to actual success count
        old_batch_size = self.current_batch_size
        success_count = len(successful_results)
        
        # New batch size = how many actually succeeded (with bounds)
        self.current_batch_size = max(success_count, self.min_batch_size)
        self.current_batch_size = min(self.current_batch_size, self.max_batch_size)
        
        # Track size history
        self.size_history.append({
            "attempt": self.batch_attempts + 1,
            "old_size": old_batch_size,
            "new_size": self.current_batch_size,
            "successful": success_count,
            "rate_limited": len(new_failed_tools),
            "other_errors": other_errors
        })
        
        # Update queue position (advance by NEW successful tools only, not retries)
        retry_tool_indices = {tool.get("tool_index") for tool in self.failed_tools}
        new_tools_processed = sum(
            1 for result in successful_results
            if int(result["tool_id"].replace("tool_", "")) not in retry_tool_indices
        )
        self.queue_position += new_tools_processed
        
        # Store failed tools for next batch
        self.failed_tools = new_failed_tools
        
        # Logging
        if rate_limit_count > 0:
            logger.warning(f"⚠️ Rate limits: {len(new_failed_tools)} tools failed, {success_count} succeeded")
            logger.info(f"📉 Adjusting batch size: {old_batch_size} → {self.current_batch_size} (based on success count)")
        elif other_errors > 0:
            logger.warning(f"⚠️ Other errors: {other_errors} tools had non-rate-limit errors")
        
        if rate_limit_count == 0 and self.current_batch_size != old_batch_size:
            logger.info(f"📊 Batch size: {old_batch_size} → {self.current_batch_size} ({success_count} successful)")

    def _has_more_to_process(self) -> bool:
        """Check if there are more tools to process."""
        return self.queue_position < len(self.all_tools) or len(self.failed_tools) > 0

    def _log_final_stats(self) -> None:
        """Log final processing statistics."""
        logger.info("\n🎯 SLIDING WINDOW STATS:")
        logger.info("========================================")
        logger.info(f"📊 Total batches: {self.batch_attempts}")
        logger.info(f"✅ Tools processed: {self.total_processed}")
        logger.info(f"⚠️ Rate limit failures: {self.total_failed}")
        logger.info(f"🎪 Final batch size: {self.current_batch_size}")
        logger.info("📈 Size evolution:")
        
        for entry in self.size_history:
            direction = "📈" if entry["new_size"] > entry["old_size"] else "📉" if entry["new_size"] < entry["old_size"] else "➡️"
            logger.info(f"   Batch {entry['attempt']}: {entry['old_size']} → {entry['new_size']} {direction} "
                       f"({entry['successful']} ok, {entry['rate_limited']} rate limited, {entry['other_errors']} other errors)")
        
        if len(self.size_history) >= 3:
            last_three = self.size_history[-3:]
            stable = all(entry["new_size"] == last_three[0]["new_size"] and entry["rate_limited"] == 0 for entry in last_three)
            if stable:
                logger.info(f"🎯 Converged to optimal batch size: {self.current_batch_size}")
        
        logger.info("========================================\n")


class LLMAnalyzer:
    """Enhanced LLM-Based Tool Analyzer for AI Agent Relevance."""
    
    def __init__(self, options: Optional[Dict[str, Any]] = None):
        """Initialize the LLM analyzer with configuration options."""
        if options is None:
            options = {}
            
        if Anthropic is None:
            raise ImportError("anthropic package is required for LLM analysis. Install with: pip install anthropic")
            
        api_key = options.get("api_key") or options.get("apiKey")
        if not api_key:
            raise ValueError("Anthropic API key is required for LLM analysis")
            
        self.anthropic = Anthropic(api_key=api_key)
        self.model = options.get("model", "claude-3-5-sonnet-20241022")
        self.max_retries = options.get("max_retries", 3)
        self.timeout_ms = options.get("timeout_ms", 30000)
        
        # Performance tracking
        self.stats = {
            "total_tokens_used": 0,
            "total_api_calls": 0,
            "total_retries": 0,
            "total_timeouts": 0,
            "analysis_start_time": None,
            "phase_timings": {},
            "average_tokens_per_tool": 0
        }

    async def enhance_analysis(self, pattern_analysis: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Main enhancement function - replaces the existing enhance_analysis

        Args:
            pattern_analysis: Results from pattern analysis
            context: Analysis context (purpose, user_type, risk_tolerance)

        Returns:
            Enhanced analysis results
        """
        if context is None:
            context = {}

        self.stats["analysis_start_time"] = time.time()
        tool_count = len(pattern_analysis["tools"])

        logger.info(f"🤖 Starting focused AI analysis for {tool_count} tools...")
        logger.info(f"📊 Analysis context: {json.dumps(context)}")
        logger.info(f"⚙️  Configuration: model={self.model}, maxRetries={self.max_retries}, timeout={self.timeout_ms}ms")

        try:
            # Phase 1: Individual tool analysis (parallel, unlimited)
            logger.info("🔍 Phase 1: Analyzing individual tools...")
            phase1_start = time.time()
            individual_analyses = await self.analyze_individual_tools(pattern_analysis["tools"])
            self.stats["phase_timings"]["individual_analysis"] = (time.time() - phase1_start) * 1000
            logger.info(f"✅ Phase 1 complete in {self.stats['phase_timings']['individual_analysis']:.0f}ms")

            # Phase 2: Macro analysis (sequential)
            logger.info("📊 Phase 2: Running macro analysis...")
            phase2_start = time.time()
            macro_analysis = await self.analyze_macro_patterns(individual_analyses)
            self.stats["phase_timings"]["macro_analysis"] = (time.time() - phase2_start) * 1000
            logger.info(f"✅ Phase 2 complete in {self.stats['phase_timings']['macro_analysis']:.0f}ms")

            # Phase 3: Post-processing with computed metrics
            logger.info("🧮 Phase 3: Computing metrics...")
            phase3_start = time.time()
            enhanced_macro_analysis = self._enhance_with_metrics(macro_analysis, individual_analyses)
            self.stats["phase_timings"]["metrics_computation"] = (time.time() - phase3_start) * 1000
            logger.info(f"✅ Phase 3 complete in {self.stats['phase_timings']['metrics_computation']:.0f}ms")

            # Phase 4: Merge all results
            logger.info("🔗 Phase 4: Merging results...")
            phase4_start = time.time()
            final_result = self._merge_all_analyses(
                pattern_analysis,
                individual_analyses,
                enhanced_macro_analysis
            )
            self.stats["phase_timings"]["result_merging"] = (time.time() - phase4_start) * 1000
            logger.info(f"✅ Phase 4 complete in {self.stats['phase_timings']['result_merging']:.0f}ms")

            total_time = (time.time() - self.stats["analysis_start_time"]) * 1000
            self._log_final_stats(tool_count, len(individual_analyses), total_time)

            return final_result

        except Exception as error:
            total_time = (time.time() - self.stats["analysis_start_time"]) * 1000
            logger.error(f"🚨 LLM analysis failed: {str(error)}")
            logger.error(f"📊 Failure stats: totalTime={total_time:.0f}ms, apiCalls={self.stats['total_api_calls']}, "
                        f"retries={self.stats['total_retries']}, timeouts={self.stats['total_timeouts']}, "
                        f"tokensUsed={self.stats['total_tokens_used']}")
            return self._create_fallback_result(pattern_analysis, error)

    async def analyze_individual_tools(self, tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Phase 1: Analyze tools with sliding window processing - no individual retries!

        Args:
            tools: List of tools to analyze

        Returns:
            List of individual analysis results
        """
        start_time = time.time()

        logger.info(f"🔍 Analyzing {len(tools)} tools with sliding window processing...")
        logger.info("⚡ Processing as fast as API limits allow")

        # Create sliding window processor with smart initial configuration
        processor = SlidingWindowProcessor({
            "initial_batch_size": 25 if len(tools) > 100 else 15,
            "max_batch_size": 35,
            "min_batch_size": 5
        })

        try:
            # Use sliding window processing - no more individual retries!
            individual_analyses = await processor.process_all_tools(tools, self)

            duration = (time.time() - start_time) * 1000
            success_count = len([r for r in individual_analyses if not r.get("error")])
            success_rate = (success_count / len(tools) * 100) if tools else 0
            tools_per_minute = (len(tools) / (duration / 60000)) if duration > 0 else 0

            logger.info(f"🎉 Sliding window analysis complete in {duration/60000:.1f} minutes:")
            logger.info(f"   📈 Success: {success_count}/{len(tools)} ({success_rate:.1f}%)")
            logger.info(f"   ⚡ Speed: {tools_per_minute:.1f} tools/minute")
            logger.info(f"   🔄 Retries: {self.stats['total_retries']}")
            logger.info(f"   💰 Tokens used: {self.stats['total_tokens_used']}")
            if self.stats["average_tokens_per_tool"] > 0:
                logger.info(f"   📊 Avg tokens/tool: {round(self.stats['average_tokens_per_tool'])}")

            return individual_analyses

        except Exception as error:
            duration = (time.time() - start_time) * 1000
            logger.error(f"🚨 Sliding window analysis failed after {duration/60000:.1f} minutes: {str(error)}")
            raise error

    async def analyze_macro_patterns(self, individual_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Enhanced macro analysis with better JSON parsing

        Args:
            individual_analyses: Results from individual tool analysis

        Returns:
            Macro analysis results
        """
        start_time = time.time()
        logger.info("📊 Running macro analysis...")

        # Prepare data for macro analysis
        macro_data = self._prepare_macro_data(individual_analyses)
        logger.info("📋 Macro data prepared:")
        logger.info(f"   🎯 High relevance tools (80+): {macro_data['high_relevance_count']}/{macro_data['total_count']}")
        logger.info(f"   🏷️  Workflow tags found: {len(macro_data['workflow_tag_counts'])}")
        logger.info(f"   📊 Functionality types: {', '.join(macro_data['functionality_type_counts'].keys())}")

        # Build macro analysis prompt
        prompt = self._build_macro_analysis_prompt(macro_data)
        logger.info(f"📝 Macro prompt length: {len(prompt)} characters")

        try:
            self.stats["total_api_calls"] += 1
            api_start_time = time.time()

            response = await asyncio.wait_for(
                self.anthropic.messages.create(
                    model=self.model,
                    max_tokens=4000,
                    temperature=0.2,
                    system=self._get_macro_analysis_system_prompt(),
                    messages=[{"role": "user", "content": prompt}]
                ),
                timeout=self.timeout_ms / 1000
            )

            api_duration = (time.time() - api_start_time) * 1000

            # Track token usage if available
            if hasattr(response, 'usage') and response.usage:
                tokens_used = response.usage.input_tokens + response.usage.output_tokens
                self.stats["total_tokens_used"] += tokens_used
                logger.info(f"💰 Macro analysis tokens: {response.usage.input_tokens} in + {response.usage.output_tokens} out")

            # Enhanced JSON parsing for macro analysis
            response_text = response.content[0].text
            macro_analysis = self._extract_and_parse_json(response_text)

            # Validate the structure
            self._validate_macro_analysis(macro_analysis)

            duration = (time.time() - start_time) * 1000
            logger.info(f"✅ Macro analysis complete in {duration:.0f}ms (API: {api_duration:.0f}ms)")
            logger.info(f"   🔗 Workflow groups: {len(macro_analysis['workflow_groups'])}")
            logger.info(f"   📋 Selection templates: {len(macro_analysis['selection_templates'])}")

            # Log sample workflow groups
            for i, (key, workflow) in enumerate(macro_analysis["workflow_groups"].items()):
                if i >= 3:
                    break
                logger.info(f"   📁 {workflow['name']}: {len(workflow['tools'])} tools ({workflow['priority']} priority)")

            return {
                **macro_analysis,
                "analysis_metadata": {
                    "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                    "tools_analyzed": len(individual_analyses),
                    "high_relevance_count": macro_data["high_relevance_count"],
                    "processing_time_ms": duration,
                    "api_time_ms": api_duration
                }
            }

        except Exception as error:
            duration = (time.time() - start_time) * 1000
            logger.error(f"🚨 Macro analysis failed after {duration:.0f}ms: {str(error)}")
            logger.error(f"📋 Macro data summary: totalTools={macro_data['total_count']}, "
                        f"highRelevance={macro_data['high_relevance_count']}, "
                        f"workflowTags={len(macro_data['workflow_tag_counts'])}")
            return self._create_fallback_macro_analysis(individual_analyses)

    def _prepare_macro_data(self, individual_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Prepare data for macro analysis

        Args:
            individual_analyses: Results from individual tool analysis

        Returns:
            Prepared macro data for analysis
        """
        sorted_by_relevance = sorted(
            individual_analyses,
            key=lambda x: x.get("relevance_score", 0),
            reverse=True
        )

        return {
            "total_count": len(individual_analyses),
            "high_relevance_count": len([a for a in individual_analyses if a.get("relevance_score", 0) >= 80]),
            "core_count": len([a for a in individual_analyses if a.get("functionality_type") == "core"]),
            "supporting_count": len([a for a in individual_analyses if a.get("functionality_type") == "supporting"]),
            "top25_tools": [
                {
                    "name": t.get("tool_id", ""),
                    "relevance": t.get("relevance_score", 0),
                    "type": t.get("functionality_type", ""),
                    "workflows": t.get("workflow_tags", [])
                }
                for t in sorted_by_relevance[:25]
            ],
            "workflow_tag_counts": self._calculate_workflow_tag_counts(individual_analyses),
            "functionality_type_counts": self._calculate_functionality_type_counts(individual_analyses)
        }

    def _build_macro_analysis_prompt(self, macro_data: Dict[str, Any]) -> str:
        """
        Build macro analysis prompt

        Args:
            macro_data: Prepared macro data

        Returns:
            Formatted prompt for macro analysis
        """
        top25_tools_str = "\n".join([
            f"- {t['name']} ({t['relevance']}) [{t['type']}] - Workflows: {', '.join(t['workflows'])}"
            for t in macro_data["top25_tools"]
        ])

        workflow_tags_str = "\n".join([
            f"- {tag}: {count} tools"
            for tag, count in macro_data["workflow_tag_counts"].items()
        ])

        functionality_types_str = "\n".join([
            f"- {type_name}: {count} tools"
            for type_name, count in macro_data["functionality_type_counts"].items()
        ])

        return f"""Based on individual tool analyses, create workflow groupings and selection templates:

## ANALYZED TOOLS SUMMARY
Total Tools: {macro_data['total_count']}
High Relevance (80+): {macro_data['high_relevance_count']}
Core Functionality: {macro_data['core_count']}
Supporting Functionality: {macro_data['supporting_count']}

## TOP 25 TOOLS BY RELEVANCE
{top25_tools_str}

## WORKFLOW TAG DISTRIBUTION
{workflow_tags_str}

## FUNCTIONALITY TYPE DISTRIBUTION
{functionality_types_str}

## ANALYSIS TASKS

1. **IDENTIFY CORE WORKFLOWS**: Group related tools into logical workflows
2. **CREATE SELECTION TEMPLATES**: Recommend tool selections for different use cases (NO tool count limits)
3. **PRIORITIZE BY RELEVANCE**: Rank workflows by importance for AI agents

**IMPORTANT**: The JSON structure below shows the required format with EXAMPLE values.
Do NOT use the example values literally - analyze the actual tools and provide real results.
Do NOT include any computed metrics like averages, counts, or scores - focus on semantic analysis only.

{{
  "workflow_groups": {{
    "<actual_workflow_name>": {{
      "name": "<descriptive workflow name>",
      "description": "<what this workflow accomplishes>",
      "tools": ["<actual_tool_ids_from_the_analysis_above>"],
      "priority": "<high|medium|low based on AI agent importance>",
      "use_cases": ["<specific use cases for AI agents>"]
    }}
  }},
  "selection_templates": {{
    "<template_name>": {{
      "name": "<template name>",
      "description": "<what this template provides>",
      "selection_criteria": {{
        "min_relevance_score": <number>,
        "required_functionality_types": ["<types>"],
        "required_workflows": ["<workflow_names>"]
      }},
      "selected_tools": ["<actual_tool_ids_that_meet_criteria>"],
      "reasoning": "<why these specific tools were selected>"
    }}
  }},
  "recommendations": {{
    "highest_value_tools": ["<tool_id_1>", "<tool_id_2>"],
    "workflow_priorities": ["<workflow_1>", "<workflow_2>"],
    "complexity_warnings": ["<warning_1>"],
    "safety_exclusions": ["<tool_id>"],
    "reasoning": "<overall recommendation reasoning>"
  }}
}}"""

    def _get_macro_analysis_system_prompt(self) -> str:
        """
        Get system prompt for macro analysis

        Returns:
            System prompt for macro analysis
        """
        return """You are an expert API strategist. You analyze collections of API endpoints to identify core workflows and create practical tool selection templates for AI agents.

Your goal is to group related tools into logical workflows and recommend optimal tool selections for different use cases.

Respond with valid JSON only."""

    def _enhance_with_metrics(self, macro_analysis: Dict[str, Any], individual_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Phase 3: Enhance macro analysis with computed metrics

        Args:
            macro_analysis: Results from macro analysis
            individual_analyses: Results from individual tool analysis

        Returns:
            Enhanced macro analysis with computed metrics
        """
        start_time = time.time()
        logger.info("🧮 Computing metrics for workflows and templates...")

        # Enhance workflow groups with computed metrics
        workflow_count = 0
        for workflow_key, workflow in macro_analysis.get("workflow_groups", {}).items():
            workflow_tool_analyses = [
                analysis for analysis in individual_analyses
                if analysis.get("tool_id") in workflow.get("tools", [])
            ]

            workflow["computed_metrics"] = {
                "tool_count": len(workflow.get("tools", [])),
                "average_relevance": self._calculate_average([
                    a.get("relevance_score", 0) for a in workflow_tool_analyses
                ]),
                "complexity_distribution": self._get_complexity_distribution(workflow_tool_analyses),
                "risk_distribution": self._get_risk_distribution(workflow_tool_analyses)
            }

            workflow_count += 1

        # Enhance selection templates with computed metrics
        template_count = 0
        for template_key, template in macro_analysis.get("selection_templates", {}).items():
            template_tool_analyses = [
                analysis for analysis in individual_analyses
                if analysis.get("tool_id") in template.get("selected_tools", [])
            ]

            template["computed_quality"] = {
                "tool_count": len(template.get("selected_tools", [])),
                "average_relevance": self._calculate_average([
                    a.get("relevance_score", 0) for a in template_tool_analyses
                ]),
                "workflow_coverage": self._calculate_workflow_coverage(
                    template_tool_analyses, macro_analysis.get("workflow_groups", {})
                ),
                "coverage_score": self._calculate_coverage_score(
                    template_tool_analyses, macro_analysis.get("workflow_groups", {})
                ),
                "complexity_breakdown": self._get_complexity_distribution(template_tool_analyses)
            }

            template_count += 1

        duration = (time.time() - start_time) * 1000
        logger.info(f"✅ Metrics computed in {duration:.0f}ms:")
        logger.info(f"   📊 Enhanced {workflow_count} workflow groups")
        logger.info(f"   📋 Enhanced {template_count} selection templates")

        return macro_analysis

    def _merge_all_analyses(self, pattern_analysis: Dict[str, Any], individual_analyses: List[Dict[str, Any]],
                           macro_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Phase 4: Merge all analysis phases into final enhanced result

        Args:
            pattern_analysis: Results from pattern analysis
            individual_analyses: Results from individual analysis
            macro_analysis: Results from macro analysis

        Returns:
            Final merged analysis results
        """
        start_time = time.time()
        logger.info("🔗 Merging all analysis results...")

        # Create enhanced tools with all analysis data
        enhanced_tools = []
        for index, tool in enumerate(pattern_analysis["tools"]):
            individual_analysis = next(
                (a for a in individual_analyses if a.get("tool_id") == f"tool_{index}"),
                None
            )
            workflow_group = self._find_workflow_group(individual_analysis, macro_analysis)
            template_recommendations = self._find_template_recommendations(individual_analysis, macro_analysis)

            enhanced_tool = {
                # Original pattern analysis data
                **tool,

                # Individual LLM analysis
                "relevance_score": individual_analysis.get("relevance_score", 50) if individual_analysis else 50,
                "functionality_type": individual_analysis.get("functionality_type", "supporting") if individual_analysis else "supporting",
                "ai_use_cases": individual_analysis.get("ai_use_cases", []) if individual_analysis else [],
                "workflow_tags": individual_analysis.get("workflow_tags", []) if individual_analysis else [],
                "complexity_level": individual_analysis.get("complexity_level", "moderate") if individual_analysis else "moderate",
                "impact_scope": individual_analysis.get("impact_scope", "self-only") if individual_analysis else "self-only",
                "risk_assessment": individual_analysis.get("risk_assessment", "medium") if individual_analysis else "medium",
                "requires_context": individual_analysis.get("requires_context", []) if individual_analysis else [],

                # Macro analysis connections
                "workflow_group": workflow_group,
                "recommended_in_templates": template_recommendations,
                "priority_rank": self._calculate_priority_rank(individual_analysis, macro_analysis)
            }
            enhanced_tools.append(enhanced_tool)

        # Calculate final statistics
        relevance_stats = self._calculate_relevance_stats(enhanced_tools)
        workflow_stats = self._calculate_workflow_stats(macro_analysis)

        # DETAILED LOGGING FOR DEBUGGING
        logger.info("\n🎯 DETAILED ANALYSIS RESULTS:")
        logger.info("========================================")

        # Log high relevance tools
        high_relevance_tools = [t for t in enhanced_tools if t.get("relevance_score", 0) >= 80]
        logger.info(f"🌟 HIGH RELEVANCE TOOLS (80+): {len(high_relevance_tools)}")
        for tool in high_relevance_tools:
            logger.info(f"   • {tool.get('name', 'unnamed')} ({tool.get('relevance_score', 0)}) - "
                       f"{tool.get('functionality_type', 'unknown')} - "
                       f"{tool.get('method', 'GET')} {tool.get('path', '')}")

        # Log workflow groups with their tools
        workflow_groups = macro_analysis.get("workflow_groups", {})
        logger.info(f"\n📊 WORKFLOW GROUPS: {len(workflow_groups)}")
        for key, workflow in workflow_groups.items():
            logger.info(f"   📁 {workflow.get('name', key)} ({workflow.get('priority', 'unknown')} priority):")
            logger.info(f"      Tools: {len(workflow.get('tools', []))}")
            for tool_id in workflow.get("tools", [])[:5]:
                tool = next((t for t in enhanced_tools if f"tool_{enhanced_tools.index(t)}" == tool_id), None)
                if tool:
                    logger.info(f"      • {tool.get('name', 'unnamed')} ({tool.get('relevance_score', 0)})")
            if len(workflow.get("tools", [])) > 5:
                logger.info(f"      ... and {len(workflow.get('tools', [])) - 5} more")

        # Log selection templates with their tools
        selection_templates = macro_analysis.get("selection_templates", {})
        logger.info(f"\n📋 SELECTION TEMPLATES: {len(selection_templates)}")
        for key, template in selection_templates.items():
            logger.info(f"   📝 {template.get('name', key)}:")
            logger.info(f"      Description: {template.get('description', 'No description')}")
            logger.info(f"      Tools: {len(template.get('selected_tools', []))}")
            criteria = template.get("selection_criteria", {})
            logger.info(f"      Min relevance: {criteria.get('min_relevance_score', 'N/A')}")
            for tool_id in template.get("selected_tools", [])[:5]:
                tool = next((t for t in enhanced_tools if f"tool_{enhanced_tools.index(t)}" == tool_id), None)
                if tool:
                    logger.info(f"      • {tool.get('name', 'unnamed')} ({tool.get('relevance_score', 0)})")
            if len(template.get("selected_tools", [])) > 5:
                logger.info(f"      ... and {len(template.get('selected_tools', [])) - 5} more")

        logger.info("========================================\n")

        duration = (time.time() - start_time) * 1000
        logger.info(f"✅ Results merged in {duration:.0f}ms:")
        logger.info(f"   🎯 Tool relevance distribution: {relevance_stats}")
        logger.info(f"   📊 Workflow distribution: {workflow_stats}")

        return {
            # Original pattern analysis
            "summary": pattern_analysis["summary"],

            # Enhanced tools
            "tools": enhanced_tools,

            # Macro analysis results
            "workflow_groups": macro_analysis.get("workflow_groups", {}),
            "selection_templates": macro_analysis.get("selection_templates", {}),
            "recommendations": macro_analysis.get("recommendations", {}),

            # Metadata
            "analysis_metadata": {
                "pattern_analysis_timestamp": time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "individual_analysis_count": len(individual_analyses),
                "macro_analysis_timestamp": macro_analysis.get("analysis_metadata", {}).get("timestamp"),
                "total_processing_time": (time.time() - self.stats["analysis_start_time"]) * 1000,
                "phase_timings": self.stats["phase_timings"],
                "api_stats": {
                    "total_calls": self.stats["total_api_calls"],
                    "total_retries": self.stats["total_retries"],
                    "total_timeouts": self.stats["total_timeouts"],
                    "total_tokens": self.stats["total_tokens_used"]
                }
            }
        }

    # === HELPER METHODS ===

    def _log_final_stats(self, original_tool_count: int, processed_tool_count: int, total_time: float) -> None:
        """Log final comprehensive stats."""
        logger.info("\n🎉 LLM Analysis Complete!")
        logger.info("========================================")
        logger.info(f"⏱️  Total time: {total_time:.0f}ms ({total_time/1000:.1f}s)")
        logger.info(f"🎯 Tools processed: {processed_tool_count}/{original_tool_count}")
        logger.info(f"📞 API calls made: {self.stats['total_api_calls']}")
        logger.info(f"🔄 Total retries: {self.stats['total_retries']}")
        logger.info(f"⏰ Timeouts: {self.stats['total_timeouts']}")
        logger.info(f"💰 Tokens used: {self.stats['total_tokens_used']}")
        logger.info("\n⚡ Phase breakdown:")
        for phase, time_ms in self.stats["phase_timings"].items():
            percentage = (time_ms / total_time * 100) if total_time > 0 else 0
            logger.info(f"   {phase}: {time_ms:.0f}ms ({percentage:.1f}%)")

        if self.stats["total_tokens_used"] > 0 and processed_tool_count > 0:
            avg_tokens_per_tool = round(self.stats["total_tokens_used"] / processed_tool_count)
            logger.info("\n💡 Efficiency metrics:")
            logger.info(f"   Avg tokens/tool: {avg_tokens_per_tool}")
            logger.info(f"   Tools/second: {(processed_tool_count / (total_time/1000)):.2f}")
        logger.info("========================================\n")

    def _calculate_relevance_stats(self, tools: List[Dict[str, Any]]) -> Dict[str, int]:
        """Calculate relevance statistics."""
        ranges = {"high (80+)": 0, "medium (50-79)": 0, "low (<50)": 0}
        for tool in tools:
            score = tool.get("relevance_score", 0)
            if score >= 80:
                ranges["high (80+)"] += 1
            elif score >= 50:
                ranges["medium (50-79)"] += 1
            else:
                ranges["low (<50)"] += 1
        return ranges

    def _calculate_workflow_stats(self, macro_analysis: Dict[str, Any]) -> Dict[str, int]:
        """Calculate workflow statistics."""
        priorities = {"high": 0, "medium": 0, "low": 0}
        for workflow in macro_analysis.get("workflow_groups", {}).values():
            priority = workflow.get("priority", "medium")
            priorities[priority] = priorities.get(priority, 0) + 1
        return priorities

    def _create_default_analysis(self, tool_id: str) -> Dict[str, Any]:
        """Create default analysis for failed tools."""
        return {
            "relevance_score": 50,
            "functionality_type": "supporting",
            "ai_use_cases": ["General API functionality"],
            "workflow_tags": ["uncategorized"],
            "complexity_level": "moderate",
            "impact_scope": "self-only",
            "risk_assessment": "medium",
            "requires_context": [],
            "reasoning": "Default analysis due to processing failure"
        }

    def _create_fallback_result(self, pattern_analysis: Dict[str, Any], error: Exception) -> Dict[str, Any]:
        """Create fallback result when LLM analysis fails."""
        logger.warning("🔄 Creating fallback analysis result")

        enhanced_tools = []
        for tool in pattern_analysis["tools"]:
            enhanced_tool = {
                **tool,
                "relevance_score": 60,  # Moderate default
                "functionality_type": "supporting",
                "ai_use_cases": ["General API functionality"],
                "workflow_tags": ["uncategorized"],
                "complexity_level": "moderate",
                "impact_scope": "self-only",
                "risk_assessment": "medium"
            }
            enhanced_tools.append(enhanced_tool)

        return {
            **pattern_analysis,
            "tools": enhanced_tools,
            "workflow_groups": {},
            "selection_templates": {},
            "recommendations": {},
            "error": {
                "message": str(error),
                "fallback_used": True,
                "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
            }
        }

    def _create_fallback_macro_analysis(self, individual_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create fallback macro analysis if LLM fails."""
        # Create basic groupings based on functionality types
        core_tools = [
            a.get("tool_id", "") for a in individual_analyses
            if a.get("functionality_type") == "core"
        ]

        return {
            "workflow_groups": {
                "core-functionality": {
                    "name": "Core Functionality",
                    "description": "Essential API operations",
                    "tools": core_tools,
                    "priority": "high",
                    "use_cases": ["Basic API operations"]
                }
            },
            "selection_templates": {
                "essentials": {
                    "name": "Essential Tools",
                    "description": "Basic functionality only",
                    "selection_criteria": {
                        "min_relevance_score": 70,
                        "required_functionality_types": ["core"]
                    },
                    "selected_tools": core_tools,
                    "reasoning": "Fallback selection of core tools"
                }
            },
            "recommendations": {
                "highest_value_tools": core_tools[:10],
                "workflow_priorities": ["core-functionality"],
                "complexity_warnings": [],
                "safety_exclusions": []
            }
        }

    def _validate_macro_analysis(self, analysis: Dict[str, Any]) -> None:
        """Validate macro analysis structure."""
        required = ["workflow_groups", "selection_templates", "recommendations"]
        for field in required:
            if field not in analysis:
                raise ValueError(f"Missing required field: {field}")

    def _calculate_workflow_tag_counts(self, analyses: List[Dict[str, Any]]) -> Dict[str, int]:
        """Calculate workflow tag counts."""
        counts = {}
        for analysis in analyses:
            for tag in analysis.get("workflow_tags", []):
                counts[tag] = counts.get(tag, 0) + 1
        return counts

    def _calculate_functionality_type_counts(self, analyses: List[Dict[str, Any]]) -> Dict[str, int]:
        """Calculate functionality type counts."""
        counts = {}
        for analysis in analyses:
            type_name = analysis.get("functionality_type", "unknown")
            counts[type_name] = counts.get(type_name, 0) + 1
        return counts

    def _calculate_average(self, numbers: List[float]) -> int:
        """Calculate average of numbers."""
        if not numbers:
            return 0
        return round(sum(numbers) / len(numbers))

    def _calculate_coverage_score(self, tool_analyses: List[Dict[str, Any]], workflow_groups: Dict[str, Any]) -> int:
        """Calculate coverage score."""
        high_priority_workflows = [
            key for key, workflow in workflow_groups.items()
            if workflow.get("priority") == "high"
        ]

        if not high_priority_workflows:
            return 100

        covered_workflows = set()
        for analysis in tool_analyses:
            for tag in analysis.get("workflow_tags", []):
                if tag in high_priority_workflows:
                    covered_workflows.add(tag)

        return round((len(covered_workflows) / len(high_priority_workflows)) * 100)

    def _calculate_workflow_coverage(self, tool_analyses: List[Dict[str, Any]], workflow_groups: Dict[str, Any]) -> List[str]:
        """Calculate workflow coverage."""
        covered_workflows = set()
        for analysis in tool_analyses:
            for tag in analysis.get("workflow_tags", []):
                covered_workflows.add(tag)
        return list(covered_workflows)

    def _get_complexity_distribution(self, analyses: List[Dict[str, Any]]) -> Dict[str, int]:
        """Get complexity distribution."""
        distribution = {"simple": 0, "moderate": 0, "complex": 0}
        for analysis in analyses:
            complexity = analysis.get("complexity_level", "moderate")
            distribution[complexity] = distribution.get(complexity, 0) + 1
        return distribution

    def _get_risk_distribution(self, analyses: List[Dict[str, Any]]) -> Dict[str, int]:
        """Get risk distribution."""
        distribution = {"low": 0, "medium": 0, "high": 0}
        for analysis in analyses:
            risk = analysis.get("risk_assessment", "medium")
            distribution[risk] = distribution.get(risk, 0) + 1
        return distribution

    def _find_workflow_group(self, individual_analysis: Optional[Dict[str, Any]], macro_analysis: Dict[str, Any]) -> Optional[str]:
        """Find workflow group for a tool."""
        if not individual_analysis or not macro_analysis.get("workflow_groups"):
            return None

        for workflow_key, workflow in macro_analysis["workflow_groups"].items():
            if individual_analysis.get("tool_id") in workflow.get("tools", []):
                return workflow_key
        return None

    def _find_template_recommendations(self, individual_analysis: Optional[Dict[str, Any]], macro_analysis: Dict[str, Any]) -> List[str]:
        """Find template recommendations for a tool."""
        if not individual_analysis or not macro_analysis.get("selection_templates"):
            return []

        recommendations = []
        for template_key, template in macro_analysis["selection_templates"].items():
            if individual_analysis.get("tool_id") in template.get("selected_tools", []):
                recommendations.append(template_key)
        return recommendations

    def _calculate_priority_rank(self, individual_analysis: Optional[Dict[str, Any]], macro_analysis: Dict[str, Any]) -> int:
        """Calculate priority rank for a tool."""
        if not individual_analysis:
            return 999
        return 100 - individual_analysis.get("relevance_score", 50)  # Lower rank = higher priority

    def _extract_and_parse_json(self, text: str) -> Dict[str, Any]:
        """Enhanced JSON extraction that handles markdown code blocks."""
        try:
            # First, try to extract JSON from markdown code blocks
            json_content = self._extract_json_from_markdown(text)
            return json.loads(json_content)
        except Exception as error:
            # If that fails, try parsing the text directly
            try:
                return json.loads(text.strip())
            except json.JSONDecodeError:
                # If both fail, try to find JSON-like content in the text
                import re
                json_match = re.search(r'\{[\s\S]*\}', text)
                if json_match:
                    try:
                        return json.loads(json_match.group(0))
                    except json.JSONDecodeError:
                        raise ValueError(f"Failed to parse JSON: {str(error)}. Raw response: {text[:200]}...")
                raise ValueError(f"No valid JSON found in response: {text[:200]}...")

    def _extract_json_from_markdown(self, text: str) -> str:
        """Extract JSON content from markdown code blocks."""
        import re
        # Remove markdown code block formatting
        json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', text)
        if json_match:
            return json_match.group(1).strip()

        # If no code blocks, return the text as-is (might already be JSON)
        return text.strip()

    async def _analyze_individual_tool_once(self, tool: Dict[str, Any], tool_id: str) -> Dict[str, Any]:
        """
        Analyze single tool once - NO RETRIES (batch processor handles rate limits)

        Args:
            tool: Tool to analyze
            tool_id: Tool identifier

        Returns:
            Analysis result or error information
        """
        try:
            result = await self._analyze_individual_tool(tool, tool_id)
            return {"success": True, "result": result}
        except Exception as error:
            return {
                "success": False,
                "error": str(error),
                "is_rate_limit": "rate_limit" in str(error).lower(),
                "tool": tool,
                "tool_id": tool_id
            }

    async def _analyze_individual_tool(self, tool: Dict[str, Any], tool_id: str) -> Dict[str, Any]:
        """
        Core individual tool analysis with token tracking

        Args:
            tool: Tool to analyze
            tool_id: Tool identifier

        Returns:
            Individual tool analysis result
        """
        prompt = self._build_individual_tool_prompt(tool)

        try:
            self.stats["total_api_calls"] += 1
            start_time = time.time()

            response = await asyncio.wait_for(
                self.anthropic.messages.create(
                    model=self.model,
                    max_tokens=800,
                    temperature=0.1,
                    system=self._get_individual_tool_system_prompt(),
                    messages=[{"role": "user", "content": prompt}]
                ),
                timeout=self.timeout_ms / 1000
            )

            duration = (time.time() - start_time) * 1000
            tokens_used = 0

            # Track token usage if available
            if hasattr(response, 'usage') and response.usage:
                tokens_used = response.usage.input_tokens + response.usage.output_tokens
                self.stats["total_tokens_used"] += tokens_used

                # Update average tokens per tool
                current_average = self.stats["average_tokens_per_tool"]
                self.stats["average_tokens_per_tool"] = (
                    tokens_used if current_average == 0 else
                    (current_average * 0.8) + (tokens_used * 0.2)
                )

            # Extract and parse JSON with proper markdown handling
            response_text = response.content[0].text
            analysis = self._extract_and_parse_json(response_text)

            # Log detailed analysis for first few tools (for debugging)
            if self.stats["total_api_calls"] <= 3:
                logger.info(f"🔍 Sample analysis for {tool_id} ({duration:.0f}ms, {tokens_used} tokens):")
                logger.info(f"   Relevance: {analysis.get('relevance_score', 0)}/100")
                logger.info(f"   Type: {analysis.get('functionality_type', 'unknown')}")
                logger.info(f"   Workflows: {', '.join(analysis.get('workflow_tags', []))}")

            return {
                "tool_id": tool_id,
                "timestamp": time.strftime("%Y-%m-%dT%H:%M:%S.%fZ"),
                "processing_time_ms": duration,
                "tokens_used": tokens_used,
                **analysis
            }

        except asyncio.TimeoutError:
            raise Exception("Timeout")
        except Exception as error:
            # Handle rate limit errors specifically
            if "rate_limit" in str(error).lower():
                raise Exception(f"Rate limit: {str(error)}")

            raise Exception(f"API call failed: {str(error)}")

    def _build_individual_tool_prompt(self, tool: Dict[str, Any]) -> str:
        """Build individual tool analysis prompt."""
        endpoint_info = tool.get("endpoint_info", {})
        safety_flags = tool.get("safety_flags", {})
        auth_requirements = tool.get("auth_requirements", {})

        return f"""Analyze this API endpoint for AI agent relevance:

## ENDPOINT DETAILS
- **Name**: {tool.get('name', 'unnamed_tool')}
- **Method**: {tool.get('method', 'GET')} {tool.get('path', '')}
- **Summary**: {endpoint_info.get('summary', 'No summary')}
- **Description**: {endpoint_info.get('description', 'No description')}
- **Tags**: {', '.join(endpoint_info.get('tags', [])) or 'None'}

## INPUT REQUIREMENTS
- **Path Parameters**: {self._format_parameters(endpoint_info.get('input_schema', {}).get('path_parameters', []))}
- **Query Parameters**: {self._format_parameters(endpoint_info.get('input_schema', {}).get('query_parameters', []))}
- **Request Body**: {'Required' if endpoint_info.get('input_schema', {}).get('request_body') else 'None'}
- **Authentication**: {auth_requirements.get('auth_description', 'Required') if auth_requirements.get('auth_required') else 'None'}

## SAFETY CONTEXT
- **Risk Level**: {safety_flags.get('http_method_risk', 'UNKNOWN')}
- **Danger Signals**: {', '.join(safety_flags.get('danger_keywords', [])) or 'None'}
- **Forbidden**: {'YES' if safety_flags.get('is_forbidden') else 'NO'}

## CORE ANALYSIS QUESTIONS

1. **RELEVANCE (0-100)**: How useful is this for AI agents helping users?
   - 90-100: Essential core functionality (login, get profile, create content)
   - 70-89: Important supporting functionality (search, list items, update settings)
   - 50-69: Useful but non-essential (advanced filtering, bulk operations)
   - 30-49: Niche functionality (admin settings, edge cases)
   - 0-29: Irrelevant for typical AI agent use (system internals, debugging)

2. **FUNCTIONALITY TYPE**:
   - "core": Essential user-facing functionality
   - "supporting": Helpful but not essential
   - "admin": Administrative/management functions
   - "edge-case": Handles rare scenarios
   - "system": Internal system functions

3. **AI USE CASES**: What would an AI agent actually do with this?

4. **WORKFLOW CONTEXT**: What user workflows would need this tool?

5. **COMPLEXITY ASSESSMENT**: How hard is this for an AI to use correctly?

6. **IMPACT SCOPE**: Who/what does this affect?
   - "self-only": Only affects the requesting user
   - "team": Affects user's team/workspace
   - "organization": Affects entire organization
   - "system": System-wide effects

**IMPORTANT**: The JSON structure below shows the required format with EXAMPLE values.
Do NOT use the example values literally - provide actual analysis results.

{{
  "relevance_score": <0-100_number>,
  "functionality_type": "<core|supporting|admin|edge-case|system>",
  "ai_use_cases": [
    "<specific use case 1>",
    "<specific use case 2>"
  ],
  "workflow_tags": [
    "<workflow_category_1>",
    "<workflow_category_2>"
  ],
  "complexity_level": "<simple|moderate|complex>",
  "impact_scope": "<self-only|team|organization|system>",
  "risk_assessment": "<low|medium|high>",
  "requires_context": [
    "<context_requirement_1>"
  ],
  "reasoning": "<explanation of analysis>"
}}"""

    def _get_individual_tool_system_prompt(self) -> str:
        """Get system prompt for individual tool analysis."""
        return """You are an expert API analyst specializing in AI agent tool selection. Your job is to analyze individual API endpoints and determine their relevance for AI agents that help users accomplish tasks.

CORE CRITERIA FOR AI AGENT TOOLS:
1. RELEVANCE: Does this help AI agents get useful context or perform valuable actions?
2. CORE vs EDGE: Is this core functionality or a niche edge case?
3. SAFETY: Can AI agents use this safely without causing harm?
4. COMPLEXITY: Can AI agents use this correctly without deep domain expertise?

You must respond with valid JSON only, following the exact schema provided."""

    def _format_parameters(self, params: List[Dict[str, Any]]) -> str:
        """Format parameters for display."""
        if not params:
            return "None"
        return ", ".join([f"{p.get('name', 'unnamed')} ({p.get('type', 'string')})" for p in params])
