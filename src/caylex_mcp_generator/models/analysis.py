"""
OpenAPI analysis models.
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class OpenAPIEndpoint(BaseModel):
    """Represents an analyzed OpenAPI endpoint."""
    
    path: str = Field(description="API endpoint path")
    method: str = Field(description="HTTP method")
    operation_id: str = Field(description="OpenAPI operation ID")
    summary: str = Field(description="Endpoint summary")
    description: Optional[str] = Field(default=None, description="Detailed description")
    parameters: List[Dict[str, Any]] = Field(default_factory=list, description="Parameter definitions")
    
    # AI-enhanced analysis fields
    relevance_score: int = Field(ge=0, le=100, description="Relevance score (0-100)")
    functionality_type: str = Field(description="Type of functionality (core, utility, admin, etc.)")
    workflow_tags: List[str] = Field(default_factory=list, description="Workflow classification tags")
    complexity_level: str = Field(description="Complexity level (simple, moderate, complex)")
    recommended_in_templates: List[str] = Field(default_factory=list, description="Recommended template types")

    # Enhanced LLM analysis fields
    ai_use_cases: List[str] = Field(default_factory=list, description="AI agent use cases for this tool")
    impact_scope: str = Field(default="self-only", description="Impact scope (self-only, team, organization, system)")
    risk_assessment: str = Field(default="medium", description="Risk level (low, medium, high)")
    requires_context: List[str] = Field(default_factory=list, description="Context requirements for proper usage")
    workflow_group: Optional[str] = Field(default=None, description="Assigned workflow group")
    priority_rank: int = Field(default=50, description="Priority ranking (lower = higher priority)")

    # Original tool definition for generation
    original_tool: Dict[str, Any] = Field(description="Original OpenAPI tool definition")


class FilteredTool(BaseModel):
    """Represents a tool that was filtered out during analysis."""
    
    operation_id: str = Field(description="Operation ID of filtered tool")
    method: str = Field(description="HTTP method")
    path: str = Field(description="API path")
    reason: str = Field(description="Reason for filtering")
    unresolved_refs: List[str] = Field(default_factory=list, description="Unresolved schema references")


class RelevanceDistribution(BaseModel):
    """Distribution of tools by relevance score."""
    
    high: int = Field(ge=0, description="Number of high relevance tools (score >= 80)")
    medium: int = Field(ge=0, description="Number of medium relevance tools (score 50-79)")
    low: int = Field(ge=0, description="Number of low relevance tools (score < 50)")


class WorkflowGroup(BaseModel):
    """AI-enhanced workflow grouping."""

    name: str = Field(description="Group name")
    description: str = Field(description="Group description")
    tools: List[str] = Field(description="List of operation IDs in this group")
    priority: int = Field(ge=1, le=10, description="Group priority (1-10)")
    use_cases: List[str] = Field(default_factory=list, description="Specific use cases for AI agents")

    # Support both string and int priority for compatibility
    @classmethod
    def __get_validators__(cls):
        yield cls.validate_priority

    @classmethod
    def validate_priority(cls, v):
        if isinstance(v, dict) and 'priority' in v:
            priority_val = v['priority']
            if isinstance(priority_val, str):
                priority_map = {"high": 8, "medium": 5, "low": 2}
                v['priority'] = priority_map.get(priority_val.lower(), 5)
        return v


class SelectionTemplate(BaseModel):
    """Template recommendation for tool selection."""

    name: str = Field(description="Template name")
    description: str = Field(description="Template description")
    recommended_tools: List[str] = Field(description="List of recommended operation IDs")
    use_case: str = Field(description="Primary use case for this template")

    # Additional fields from LLM analyzer
    selected_tools: List[str] = Field(default_factory=list, description="Selected tools (alias for recommended_tools)")
    selection_criteria: Dict[str, Any] = Field(default_factory=dict, description="Selection criteria used")
    reasoning: str = Field(default="", description="Reasoning for tool selection")

    def __init__(self, **data):
        # Handle both selected_tools and recommended_tools
        if 'selected_tools' in data and 'recommended_tools' not in data:
            data['recommended_tools'] = data['selected_tools']
        elif 'recommended_tools' in data and 'selected_tools' not in data:
            data['selected_tools'] = data['recommended_tools']
        super().__init__(**data)


class AIRecommendation(BaseModel):
    """AI-generated recommendations for the API."""
    
    summary: str = Field(description="Overall API summary")
    strengths: List[str] = Field(description="API strengths")
    potential_issues: List[str] = Field(description="Potential issues or limitations")
    suggested_improvements: List[str] = Field(description="Suggested improvements")
    best_practices: List[str] = Field(description="Recommended best practices")


class EnhancedAnalysis(BaseModel):
    """AI-enhanced analysis results."""
    
    workflow_groups: Dict[str, WorkflowGroup] = Field(description="Workflow-based tool groupings")
    selection_templates: Dict[str, SelectionTemplate] = Field(description="Template recommendations")
    recommendations: AIRecommendation = Field(description="AI-generated recommendations")
    high_relevance_tools: List[str] = Field(description="Tools with score >= 80")
    relevance_distribution: RelevanceDistribution = Field(description="Relevance score distribution")


class AnalysisResult(BaseModel):
    """Complete OpenAPI analysis result."""
    
    openapi: Dict[str, Any] = Field(description="Original OpenAPI specification")
    endpoints: List[OpenAPIEndpoint] = Field(description="Analyzed endpoints")
    servers: List[str] = Field(description="API server URLs")
    tool_count: int = Field(ge=0, description="Total number of tools analyzed")
    complexity: str = Field(description="Overall API complexity (simple, moderate, complex)")
    filtered_tools: List[FilteredTool] = Field(default_factory=list, description="Tools filtered out during analysis")
    enhanced_analysis: EnhancedAnalysis = Field(description="AI-enhanced analysis results")
