"""
MCP server generation models.
"""

from enum import Enum
from typing import Dict, List, Optional

from pydantic import BaseModel, Field


class TransportType(str, Enum):
    """Available transport types for MCP servers."""
    STDIO = "stdio"
    STREAMABLE_HTTP = "streamable-http"
    SSE = "sse"


class FileType(str, Enum):
    """Generated file types."""
    PYTHON = "python"
    JSON = "json"
    MARKDOWN = "markdown"
    DOCKERFILE = "dockerfile"
    YAML = "yaml"
    TOML = "toml"
    TEXT = "text"


class GenerationConfig(BaseModel):
    """Configuration for MCP server generation."""

    server_name: str = Field(description="Generated server name")
    server_version: str = Field(default="1.0.0", description="Server version")
    transport: TransportType = Field(default=TransportType.STDIO, description="Transport protocol")
    port: int = Field(default=8000, ge=1, le=65535, description="Server port (for HTTP transports)")
    name: str = Field(description="Human-readable server name")
    description: str = Field(description="Server description")

    # Optional configuration
    include_auth: bool = Field(default=False, description="Include authentication middleware")
    include_logging: bool = Field(default=True, description="Include logging middleware")
    include_cors: bool = Field(default=True, description="Include CORS middleware")
    python_version: str = Field(default="3.12", description="Target Python version")
    use_poetry: bool = Field(default=False, description="Use Poetry for dependency management")
    include_docker: bool = Field(default=True, description="Include Docker configuration")


class GeneratedFile(BaseModel):
    """Represents a generated file."""
    
    name: str = Field(description="File name")
    type: FileType = Field(description="File type")
    path: str = Field(description="Relative path within the generated project")
    content: Optional[str] = Field(default=None, description="File content (if requested)")
    size: int = Field(ge=0, description="File size in bytes")


class GenerationMetadata(BaseModel):
    """Metadata about the generation process."""
    
    generator: str = Field(default="caylex-mcp-generator", description="Generator identifier")
    tool_count: int = Field(ge=0, description="Number of tools generated")
    transport: TransportType = Field(description="Selected transport type")
    next_steps: List[str] = Field(description="Recommended next steps for deployment")
    dependencies: List[str] = Field(default_factory=list, description="Required dependencies")
    environment_variables: Dict[str, str] = Field(default_factory=dict, description="Required environment variables")


class GenerationRequest(BaseModel):
    """Request for MCP server generation."""
    
    session_id: str = Field(description="Session identifier")
    config: GenerationConfig = Field(description="Generation configuration")


class GenerationResponse(BaseModel):
    """Response for MCP server generation."""
    
    success: bool = Field(description="Whether generation was successful")
    progress: int = Field(ge=0, le=100, description="Generation progress percentage")
    files: List[GeneratedFile] = Field(description="List of generated files")
    download_url: str = Field(description="URL to download the generated server")
    metadata: GenerationMetadata = Field(description="Generation metadata")
    error_message: Optional[str] = Field(default=None, description="Error message if generation failed")


class DeploymentFile(BaseModel):
    """Represents a deployment configuration file."""
    
    name: str = Field(description="File name")
    content: str = Field(description="File content")
    description: Optional[str] = Field(default=None, description="File description")


class DownloadRequest(BaseModel):
    """Request for enhanced download with deployment files."""
    
    deployment_files: List[DeploymentFile] = Field(description="Additional deployment files to include")
