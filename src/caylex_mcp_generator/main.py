"""
Main FastMCP application for the Caylex MCP Generator service.
"""

import asyncio
import json
import os
import sys
from contextlib import asynccontextmanager
from datetime import datetime, timezone
from typing import AsyncGenerator

import anthropic
import structlog
import uvicorn
from fastapi import FastAPI, HTTPException, UploadFile, File, Form, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastmcp import FastMCP

from .config import settings
from .models.api import HealthResponse, ErrorResponse, UploadResponse, IntegrationRequest, IntegrationResponse
from .models.session import CreateSessionResponse, SessionResponse, SessionStatus
from .models.curation import CurationRequest, CurationResponse
from .models.generation import GenerationRequest, GenerationResponse, DownloadRequest, DeploymentFile
from .models.analysis import AnalysisResult, OpenAPIEndpoint
from .services.session_manager import session_manager
from .services.openapi_analyzer import OpenAPIAnalyzer
from .services.tool_curator import ToolCurator
from .services.code_generator import CodeGenerator
from .utils.error_handlers import setup_error_handlers
from .utils.validation import (
    validate_session_id,
    validate_file_upload,
    validate_server_name,
    validate_content_length,
    ValidationError,
)

# Configure structured logging with line numbers
def add_caller_info(logger, method_name, event_dict):  # noqa: ARG001
    """Add caller information (filename and line number) to log entries."""
    import inspect
    frame = inspect.currentframe()
    try:
        # Walk up the stack to find the actual caller (skip structlog internals)
        while frame:
            frame = frame.f_back
            if frame and frame.f_code.co_filename and not any(
                skip in frame.f_code.co_filename for skip in [
                    'structlog', 'logging', '__init__.py'
                ]
            ):
                filename = frame.f_code.co_filename.split('/')[-1]
                lineno = frame.f_lineno
                event_dict['caller'] = f"{filename}:{lineno}"
                break
    finally:
        del frame
    return event_dict

# Configure Python's standard logging first
import logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format="%(message)s",
    stream=sys.stdout,
)

structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        add_caller_info,  # Add line number information
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer() if settings.log_format == "json" else structlog.dev.ConsoleRenderer(),
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Test logging configuration
logger.debug("Logging configuration initialized", log_level=settings.log_level, log_format=settings.log_format)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:  # noqa: ARG001
    """Application lifespan manager."""
    logger.info("Starting Caylex MCP Generator service", version=settings.service_version)
    
    # Start session manager
    await session_manager.start()
    
    yield
    
    # Cleanup
    logger.info("Shutting down Caylex MCP Generator service")
    await session_manager.stop()


# Create FastAPI application
app = FastAPI(
    title="Caylex MCP Generator",
    description="A FastMCP 2.0 service for generating MCP servers from OpenAPI specifications",
    version=settings.service_version,
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create FastMCP server instance
mcp = FastMCP(
    name="caylex-mcp-generator",
    instructions="""
    This server provides tools for generating Model Context Protocol (MCP) servers from OpenAPI specifications.

    The workflow involves:
    1. Creating a session
    2. Uploading and analyzing an OpenAPI specification
    3. Curating tools based on preferences
    4. Generating the MCP server code
    5. Downloading the generated server

    Use the health endpoint to check service status.
    """,
)

# Initialize services
openapi_analyzer = OpenAPIAnalyzer()
tool_curator = ToolCurator()
code_generator = CodeGenerator()

# Setup error handlers
setup_error_handlers(app)


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="OK",
        service=settings.service_name,
        version=settings.service_version,
        timestamp=datetime.now(timezone.utc).isoformat()
    )


@app.post("/session", response_model=CreateSessionResponse)
async def create_session():
    """Create a new user session."""
    try:
        session = await session_manager.create_session()
        logger.info("Created session", session_id=str(session.session_id))

        return CreateSessionResponse(
            session_id=str(session.session_id),
            status=session.status
        )
    except ValueError as e:
        logger.error("Failed to create session", error=str(e))
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error("Unexpected error creating session", error=str(e))
        raise HTTPException(status_code=500, detail="Failed to create session")


@app.get("/session/{session_id}", response_model=SessionResponse)
async def get_session(session_id: str):
    """Get session information and status."""
    session = await session_manager.get_session(session_id)

    if session is None:
        raise HTTPException(status_code=404, detail="Session not found")

    # Check if session belongs to different project (placeholder for future auth)
    # This would be implemented with proper authentication/authorization

    return SessionResponse(
        session_id=str(session.session_id),
        status=session.status,
        created_at=session.created_at.isoformat() + "Z",
        has_analysis=session.has_analysis,
        has_curation=session.has_curation,
        has_generation=session.has_generation
    )


@app.post("/upload", response_model=UploadResponse)
async def upload_and_analyze(
    file: UploadFile = File(..., description="OpenAPI JSON/YAML file"),
    session_id: str = Form(..., description="Valid session ID")
):
    """Upload OpenAPI specification file and perform analysis."""
    # Validate session ID format
    if not validate_session_id(session_id):
        raise HTTPException(status_code=400, detail="Invalid session ID format")

    # Validate session
    session = await session_manager.get_session(session_id)
    if session is None:
        raise HTTPException(status_code=400, detail="Session not found")

    if session.is_expired():
        raise HTTPException(status_code=400, detail="Session has expired")

    logger.info("Starting OpenAPI analysis", session_id=session_id, filename=file.filename)

    # Validate file upload
    try:
        validate_file_upload(file)
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))

    # Read and validate file content
    content = await file.read()
    try:
        validate_content_length(content)
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))

    try:
        # Perform analysis
        analysis_result = await openapi_analyzer.analyze_openapi_spec(content, file.filename)

        # Store analysis results in session
        session.mark_analysis_complete(analysis_result.model_dump())
        await session_manager.update_session(session_id, session)

        logger.info(
            "Completed OpenAPI analysis",
            session_id=session_id,
            tool_count=analysis_result.tool_count,
            complexity=analysis_result.complexity,
            server_count=len(analysis_result.servers),
            high_relevance_count=len(analysis_result.enhanced_analysis.high_relevance_tools)
        )

        return UploadResponse(analysis_result=analysis_result)

    except ValueError as e:
        error_msg = f"Analysis failed: {str(e)}"
        session.update_status(SessionStatus.ERROR, error_msg)
        await session_manager.update_session(session_id, session)
        logger.error("OpenAPI analysis failed", session_id=session_id, error=str(e))
        raise HTTPException(status_code=400, detail=error_msg)

    except Exception as e:
        error_msg = f"Unexpected error during analysis: {str(e)}"
        session.update_status(SessionStatus.ERROR, error_msg)
        await session_manager.update_session(session_id, session)
        logger.error("Unexpected error in OpenAPI analysis", session_id=session_id, error=str(e))
        raise HTTPException(status_code=500, detail="Analysis failed due to internal error")


@app.post("/curate", response_model=CurationResponse)
async def curate_tools(curation_request: CurationRequest):
    """Apply tool selection and customization preferences."""
    # Validate session ID format
    if not validate_session_id(curation_request.session_id):
        raise HTTPException(status_code=400, detail="Invalid session ID format")

    # Validate session
    session = await session_manager.get_session(curation_request.session_id)
    if session is None:
        raise HTTPException(status_code=400, detail="Session not found")

    if session.is_expired():
        raise HTTPException(status_code=400, detail="Session has expired")

    logger.info("Starting tool curation", session_id=curation_request.session_id)

    # Check if analysis has been completed
    if not session.has_analysis or not session.analysis_data:
        raise HTTPException(status_code=400, detail="No analysis data found. Please upload and analyze an OpenAPI specification first")

    try:
        # Reconstruct analysis result from session data
        analysis_result = AnalysisResult(**session.analysis_data)

        # Perform curation
        curation_data = await tool_curator.curate_tools(analysis_result, curation_request)

        # Store curation results in session
        session.mark_curation_complete(curation_data)
        await session_manager.update_session(curation_request.session_id, session)

        logger.info(
            "Completed tool curation",
            session_id=curation_request.session_id,
            final_count=curation_data["final_count"],
            applied_filters=curation_data["applied_filters"]
        )

        return CurationResponse(
            success=True,
            message="Tool curation completed successfully",
            curated_tool_count=curation_data["final_count"],
            applied_filters=curation_data["applied_filters"]
        )

    except Exception as e:
        error_msg = f"Curation failed: {str(e)}"
        session.update_status(SessionStatus.ERROR, error_msg)
        await session_manager.update_session(curation_request.session_id, session)
        logger.error("Tool curation failed", session_id=curation_request.session_id, error=str(e))
        raise HTTPException(status_code=500, detail=error_msg)


@app.post("/generate", response_model=GenerationResponse)
async def generate_mcp_server(generation_request: GenerationRequest):
    """Generate MCP server code based on curated tools."""
    # Validate session ID format
    if not validate_session_id(generation_request.session_id):
        raise HTTPException(status_code=400, detail="Invalid session ID format")

    # Validate server name
    try:
        validate_server_name(generation_request.config.server_name)
    except ValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))

    # Validate session
    session = await session_manager.get_session(generation_request.session_id)
    if session is None:
        raise HTTPException(status_code=400, detail="Session not found")

    if session.is_expired():
        raise HTTPException(status_code=400, detail="Session has expired")

    logger.info("Starting MCP server generation", session_id=generation_request.session_id)

    # Check if curation has been completed
    if not session.has_curation or not session.curation_data:
        raise HTTPException(status_code=400, detail="No curation data found. Please complete tool curation first")

    try:
        # Extract curated endpoints from session data
        curation_data = session.curation_data
        if not curation_data:
            raise HTTPException(status_code=400, detail="Curation data is empty. Please complete tool curation first")

        curated_endpoints_data = curation_data.get("curated_endpoints", [])
        if not curated_endpoints_data:
            raise HTTPException(status_code=400, detail="No curated endpoints found. Please select at least one tool during curation")

        # Validate that curated_endpoints_data is a list and contains valid data
        if not isinstance(curated_endpoints_data, list):
            raise HTTPException(status_code=400, detail="Invalid curation data format. Expected list of endpoints")

        # Reconstruct endpoint objects with error handling
        curated_endpoints = []
        for i, ep_data in enumerate(curated_endpoints_data):
            try:
                if not isinstance(ep_data, OpenAPIEndpoint):
                    logger.warning("Invalid endpoint data at index", index=i, data_type=type(ep_data).__name__)
                    continue
                curated_endpoints.append(ep_data)
            except Exception as e:
                logger.warning("Failed to reconstruct endpoint", index=i, error=str(e), data=ep_data)
                continue

        if not curated_endpoints:
            raise HTTPException(status_code=400, detail="No valid endpoints could be reconstructed from curation data")

        # Get servers from original analysis with validation
        analysis_data = session.analysis_data
        if not analysis_data:
            logger.warning("No analysis data found, using default server")
            servers = ["https://api.example.com"]
        else:
            servers = analysis_data.get("servers", ["https://api.example.com"])
            if not servers or not isinstance(servers, list):
                logger.warning("Invalid or empty servers list, using default")
                servers = ["https://api.example.com"]

        logger.info(
            session_id=generation_request.session_id,
            endpoint_count=len(curated_endpoints),
            server_count=len(servers),
            server_name=generation_request.config.server_name,
            transport=generation_request.config.transport,
            python_version=generation_request.config.python_version
        )

        # Generate the MCP server
        try:
            generation_result = await code_generator.generate_mcp_server(
                endpoints=curated_endpoints,
                servers=servers,
                config=generation_request.config
            )
        except json.JSONDecodeError as e:
            error_msg = f"JSON template generation failed: {str(e)}"
            session.update_status(SessionStatus.ERROR, error_msg)
            await session_manager.update_session(generation_request.session_id, session)
            logger.error("JSON generation error", session_id=generation_request.session_id, error=str(e))
            raise HTTPException(status_code=500, detail=error_msg)

        # Store generation results in session
        session.mark_generation_complete(generation_result)
        await session_manager.update_session(generation_request.session_id, session)

        # Create download URL
        download_url = f"/download/{generation_request.session_id}"

        logger.info(
            "Completed MCP server generation",
            session_id=generation_request.session_id,
            file_count=len(generation_result["generated_files"]),
            server_name=generation_request.config.server_name
        )

        return GenerationResponse(
            success=True,
            progress=100,
            files=generation_result["generated_files"],
            download_url=download_url,
            metadata=generation_result["metadata"]
        )

    except Exception as e:
        error_msg = f"Generation failed: {str(e)}"
        session.update_status(SessionStatus.ERROR, error_msg)
        await session_manager.update_session(generation_request.session_id, session)
        logger.error("MCP server generation failed", session_id=generation_request.session_id, error=str(e))

        # Create a minimal metadata object for error response
        from .models.generation import GenerationMetadata, TransportType
        error_metadata = GenerationMetadata(
            generator="caylex-mcp-generator",
            tool_count=0,
            transport=TransportType.STDIO,
            next_steps=["Fix the error and try again"],
            dependencies=[],
            environment_variables={}
        )

        return GenerationResponse(
            success=False,
            progress=0,
            files=[],
            download_url="",
            metadata=error_metadata,
            error_message=error_msg
        )


@app.get("/download/{session_id}")
async def download_generated_server(session_id: str):
    """Download generated MCP server as ZIP file."""
    # Validate session
    session = await session_manager.get_session(session_id)
    if session is None:
        raise HTTPException(status_code=404, detail="Session not found")

    # Check if generation has been completed
    if not session.has_generation or not session.generation_data:
        raise HTTPException(status_code=404, detail="No generated server found")

    try:
        # Get ZIP file path from generation data
        generation_data = session.generation_data
        zip_path = generation_data.get("zip_path")

        if not zip_path or not os.path.exists(zip_path):
            raise HTTPException(status_code=404, detail="Generated server file not found")

        # Get server name for filename
        config_data = generation_data.get("config", {})
        server_name = config_data.get("server_name", "mcp-server")

        logger.info("Serving generated server download", session_id=session_id, zip_path=zip_path)

        return FileResponse(
            path=zip_path,
            filename=f"{server_name}.zip",
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename={server_name}.zip"}
        )

    except Exception as e:
        logger.error("Failed to serve download", session_id=session_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to serve download")


@app.post("/download/{session_id}/enhanced")
async def download_enhanced_server(session_id: str, download_request: DownloadRequest):
    """Download enhanced server with deployment files."""
    # Validate session
    session = await session_manager.get_session(session_id)
    if session is None:
        raise HTTPException(status_code=404, detail="Session not found")

    # Check if generation has been completed
    if not session.has_generation or not session.generation_data:
        raise HTTPException(status_code=404, detail="No generated server found")

    try:
        import tempfile
        import zipfile


        # Get original generation data
        generation_data = session.generation_data
        original_files = generation_data.get("generated_files", [])
        config_data = generation_data.get("config", {})
        server_name = config_data.get("server_name", "mcp-server")

        # Create enhanced ZIP with additional deployment files
        temp_dir = tempfile.mkdtemp()
        enhanced_zip_path = os.path.join(temp_dir, f"{server_name}-enhanced.zip")

        with zipfile.ZipFile(enhanced_zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Add original files
            for file_data in original_files:
                arcname = f"{server_name}/{file_data['path']}"
                zipf.writestr(arcname, file_data['content'])

            # Add deployment files
            for deploy_file in download_request.deployment_files:
                arcname = f"{server_name}/{deploy_file.name}"
                zipf.writestr(arcname, deploy_file.content)

        logger.info(
            "Serving enhanced server download",
            session_id=session_id,
            original_files=len(original_files),
            deployment_files=len(download_request.deployment_files)
        )

        return FileResponse(
            path=enhanced_zip_path,
            filename=f"{server_name}-enhanced.zip",
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename={server_name}-enhanced.zip"}
        )

    except Exception as e:
        logger.error("Failed to serve enhanced download", session_id=session_id, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to serve enhanced download")


@app.post("/integrate/add-to-directory", response_model=IntegrationResponse)
async def integrate_with_directory(integration_request: IntegrationRequest):
    """Register generated server with Caylex Directory."""
    # Validate session
    session = await session_manager.get_session(integration_request.session_id)
    if session is None:
        raise HTTPException(status_code=400, detail="Invalid session ID")

    # Check if generation has been completed
    if not session.has_generation or not session.generation_data:
        raise HTTPException(status_code=400, detail="No generated server found")

    try:
        # Get generation data
        generation_data = session.generation_data
        config_data = generation_data.get("config", {})
        metadata = generation_data.get("metadata", {})

        # Generate deployment files if requested
        deployment_files = []
        if integration_request.server_config.generate_deployment_files:
            deployment_files = await _generate_deployment_files(
                integration_request.server_config,
                config_data,
                metadata
            )

        # Generate a mock server ID (in real implementation, this would call Caylex Directory API)
        server_id = f"srv_{integration_request.session_id[:10]}"

        # Mock directory URL (in real implementation, this would be the actual directory URL)
        directory_url = f"https://directory.caylex.com/servers/{server_id}"

        logger.info(
            "Registered server with directory",
            session_id=integration_request.session_id,
            server_id=server_id,
            server_name=integration_request.server_config.server_name
        )

        return IntegrationResponse(
            success=True,
            server_id=server_id,
            deployment_files=deployment_files,
            message="Server registered successfully with Caylex Directory",
            directory_url=directory_url
        )

    except Exception as e:
        logger.error(
            "Failed to integrate with directory",
            session_id=integration_request.session_id,
            error=str(e)
        )
        return IntegrationResponse(
            success=False,
            message=f"Registration failed: {str(e)}"
        )


async def _generate_deployment_files(server_config, config_data, metadata) -> list[DeploymentFile]:  # noqa: ARG001
    """Generate deployment files for directory integration."""
    deployment_files = []

    # Generate Kubernetes deployment
    k8s_deployment = f"""apiVersion: apps/v1
kind: Deployment
metadata:
  name: {server_config.server_name}
  labels:
    app: {server_config.server_name}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {server_config.server_name}
  template:
    metadata:
      labels:
        app: {server_config.server_name}
    spec:
      containers:
      - name: {server_config.server_name}
        image: {server_config.server_name}:latest
        ports:
        - containerPort: {config_data.get('port', 3000)}
        env:
        - name: API_KEY
          valueFrom:
            secretKeyRef:
              name: {server_config.server_name}-secrets
              key: api-key
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
---
apiVersion: v1
kind: Service
metadata:
  name: {server_config.server_name}-service
spec:
  selector:
    app: {server_config.server_name}
  ports:
  - port: 80
    targetPort: {config_data.get('port', 3000)}
  type: ClusterIP
"""

    deployment_files.append(DeploymentFile(
        name="k8s-deployment.yaml",
        content=k8s_deployment,
        description="Kubernetes deployment configuration"
    ))

    # Generate Docker Compose for development
    docker_compose = f"""version: '3.8'

services:
  {server_config.server_name}:
    build: .
    container_name: {server_config.server_name}
    restart: unless-stopped
    ports:
      - "{config_data.get('port', 3000)}:{config_data.get('port', 3000)}"
    environment:
      - NODE_ENV=production
      - API_KEY=${{API_KEY}}
    volumes:
      - ./logs:/app/logs
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
"""

    deployment_files.append(DeploymentFile(
        name="docker-compose.prod.yml",
        content=docker_compose,
        description="Production Docker Compose configuration"
    ))

    # Generate deployment script
    deploy_script = f"""#!/bin/bash
# Deployment script for {server_config.server_name}

set -e

echo "Deploying {server_config.server_name}..."

# Build Docker image
docker build -t {server_config.server_name}:latest .

# Deploy with Docker Compose
docker-compose -f docker-compose.prod.yml up -d

echo "Deployment completed successfully!"
echo "Server URL: {server_config.deployment_url}"
"""

    deployment_files.append(DeploymentFile(
        name="deploy.sh",
        content=deploy_script,
        description="Deployment script for production"
    ))

    return deployment_files


@mcp.tool
async def get_service_health() -> dict:
    """Get the health status of the MCP generator service."""
    session_count = await session_manager.get_session_count()

    return {
        "status": "OK",
        "service": settings.service_name,
        "version": settings.service_version,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "active_sessions": session_count,
        "max_sessions": settings.max_sessions,
        "session_timeout": settings.session_timeout,
    }


@mcp.tool
async def create_mcp_session() -> dict:
    """Create a new session for the MCP generation workflow."""
    try:
        session = await session_manager.create_session()
        logger.info("Created MCP session", session_id=str(session.session_id))

        return {
            "session_id": str(session.session_id),
            "status": session.status.value,
            "created_at": session.created_at.isoformat() + "Z",
            "expires_at": session.expires_at.isoformat() + "Z" if session.expires_at else None,
        }
    except ValueError as e:
        return {"error": str(e)}
    except Exception as e:
        logger.error("Unexpected error creating MCP session", error=str(e))
        return {"error": "Failed to create session"}


@mcp.tool
async def get_mcp_session_status(session_id: str) -> dict:
    """Get the status of an MCP generation session."""
    session = await session_manager.get_session(session_id)

    if session is None:
        return {"error": "Session not found"}

    return {
        "session_id": str(session.session_id),
        "status": session.status.value,
        "created_at": session.created_at.isoformat() + "Z",
        "has_analysis": session.has_analysis,
        "has_curation": session.has_curation,
        "has_generation": session.has_generation,
        "last_error": session.last_error,
    }


def main():
    """Main entry point for the application."""
    from .cli import main as cli_main
    cli_main()


if __name__ == "__main__":
    main()
