"""
Configuration settings for the Caylex MCP Generator service.
"""

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Service configuration
    service_name: str = Field(default="caylex-mcp-generator", description="Service name")
    service_version: str = Field(default="1.0.0", description="Service version")
    
    # Server configuration
    host: str = Field(default="127.0.0.1", description="Server host")
    port: int = Field(default=8000, description="Server port")
    debug: bool = Field(default=False, description="Debug mode")
    
    # File upload limits
    max_file_size: int = Field(default=10 * 1024 * 1024, description="Max file size in bytes (10MB)")
    upload_timeout: int = Field(default=30, description="Upload timeout in seconds")
    
    # Session management
    session_timeout: int = Field(default=3600, description="Session timeout in seconds (1 hour)")
    max_sessions: int = Field(default=1000, description="Maximum concurrent sessions")
    
    # OpenAPI analysis
    max_endpoints: int = Field(default=500, description="Maximum endpoints to analyze")
    relevance_threshold: int = Field(default=50, description="Minimum relevance score (0-100)")
    
    # Code generation
    output_dir: str = Field(default="./generated", description="Output directory for generated files")
    template_dir: str = Field(default="./templates", description="Template directory")
    
    # Logging
    log_level: str = Field(default="DEBUG", description="Logging level")
    log_format: str = Field(default="console", description="Log format (json|console)")
    
    # External services (for future integration)
    caylex_directory_url: str = Field(default="", description="Caylex Directory API URL")
    caylex_api_key: str = Field(default="", description="Caylex API key")

    # AI/LLM services
    anthropic_api_key: str = Field(default="", description="Anthropic API key for LLM analysis")
    anthropic_model: str = Field(default="claude-3-5-sonnet-20241022", description="Anthropic model to use")
    anthropic_timeout_ms: int = Field(default=30000, description="Anthropic API timeout in milliseconds")
    anthropic_max_retries: int = Field(default=3, description="Maximum retries for Anthropic API calls")
    
    class Config:
        env_prefix = "CAYLEX_MCP_"
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()
