Metadata-Version: 2.4
Name: caylex-mcp-generator
Version: 1.0.4
Summary: Model Context Protocol generator service using FastMCP 2.0
Author-email: Caylex <<EMAIL>>
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Python: >=3.10
Description-Content-Type: text/markdown
Requires-Dist: fastmcp>=2.0.0
Requires-Dist: fastapi>=0.104.0
Requires-Dist: pydantic>=2.5.0
Requires-Dist: pydantic-settings>=2.1.0
Requires-Dist: aiofiles>=23.2.0
Requires-Dist: jsonschema>=4.20.0
Requires-Dist: pyyaml>=6.0.1
Requires-Dist: openapi-spec-validator>=0.7.1
Requires-Dist: python-multipart>=0.0.6
Requires-Dist: zipfile-deflate64>=0.2.0
Requires-Dist: python-dateutil>=2.8.2
Requires-Dist: structlog>=23.2.0
Requires-Dist: jinja2>=3.1.0
Requires-Dist: uvicorn[standard]>=0.24.0
Requires-Dist: httpx>=0.25.0
Requires-Dist: rich>=13.7.0
Requires-Dist: anthropic>=0.34.0
Provides-Extra: dev
Requires-Dist: pytest>=7.4.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: pytest-mock>=3.12.0; extra == "dev"
Requires-Dist: black>=23.0.0; extra == "dev"
Requires-Dist: isort>=5.12.0; extra == "dev"
Requires-Dist: mypy>=1.7.0; extra == "dev"

# Caylex MCP Generator

A FastMCP 2.0 service for generating Model Context Protocol (MCP) servers from OpenAPI specifications.

## Features

- **OpenAPI Analysis**: Upload and analyze OpenAPI specifications with AI-enhanced insights
- **Tool Curation**: Select and customize tools based on relevance and functionality
- **MCP Server Generation**: Generate FastMCP 2.0 compatible servers with TypeScript/JavaScript
- **Session Management**: Stateful workflow management for the generation process
- **File Downloads**: Download generated servers as ZIP files with deployment configurations
- **Directory Integration**: Register generated servers with Caylex Directory

## API Endpoints

### Health Check
- `GET /health` - Service health status

### Session Management
- `POST /sessions` - Create new session
- `GET /sessions/{sessionId}` - Get session status

### OpenAPI Analysis
- `POST /upload` - Upload and analyze OpenAPI specification

### Tool Curation
- `POST /curate` - Apply tool selection and customization

### Server Generation
- `POST /generate` - Generate MCP server code
- `GET /download/{sessionId}` - Download generated server
- `POST /download/{sessionId}/enhanced` - Download with deployment files

### Directory Integration
- `POST /integrate/add-to-directory` - Register with Caylex Directory

## Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Or install in development mode
pip install -e ".[dev]"
```

## Usage

### Running the Service

```bash
# Using uvicorn directly
uvicorn caylex_mcp_generator.main:app --host 0.0.0.0 --port 8000

# Using the CLI script
caylex-mcp-generator

# With environment variables
CAYLEX_MCP_HOST=0.0.0.0 CAYLEX_MCP_PORT=8000 caylex-mcp-generator
```

### Environment Variables

All configuration can be set via environment variables with the `CAYLEX_MCP_` prefix:

- `CAYLEX_MCP_HOST` - Server host (default: 127.0.0.1)
- `CAYLEX_MCP_PORT` - Server port (default: 8000)
- `CAYLEX_MCP_DEBUG` - Debug mode (default: false)
- `CAYLEX_MCP_MAX_FILE_SIZE` - Max upload size in bytes (default: 10MB)
- `CAYLEX_MCP_SESSION_TIMEOUT` - Session timeout in seconds (default: 3600)

## Development

```bash
# Install development dependencies
pip install -e ".[dev]"

# Run tests
pytest

# Format code
black src tests
isort src tests

# Type checking
mypy src
```

## License

MIT License - see LICENSE file for details.
