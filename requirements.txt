# FastMCP 2.0 - Core MCP framework
fastmcp>=2.0.0

# Web framework and server
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# Data validation and serialization
pydantic>=2.5.0
pydantic-settings>=2.1.0

# HTTP client for external API calls
httpx>=0.25.0
aiofiles>=23.2.0

# AI/LLM services
anthropic>=0.34.0

# OpenAPI and JSON schema handling
jsonschema>=4.20.0
pyyaml>=6.0.1
openapi-spec-validator>=0.7.1

# File handling and compression
python-multipart>=0.0.6
zipfile-deflate64>=0.2.0

# UUID and datetime utilities
python-dateutil>=2.8.2

# Logging and monitoring
structlog>=23.2.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.12.0
httpx-mock>=0.10.0

# Code generation and templating
jinja2>=3.1.0

# Optional: For enhanced error handling
rich>=13.7.0
